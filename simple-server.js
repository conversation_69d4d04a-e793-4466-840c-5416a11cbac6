const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

// Configuration
const PORT = process.env.PORT || 3000;
const HOST = '0.0.0.0';

// MIME types for different file extensions
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.wav': 'audio/wav',
    '.mp4': 'video/mp4',
    '.woff': 'application/font-woff',
    '.ttf': 'application/font-ttf',
    '.eot': 'application/vnd.ms-fontobject',
    '.otf': 'application/font-otf',
    '.wasm': 'application/wasm',
    '.data': 'application/octet-stream',
    '.symbols': 'application/octet-stream',
    '.framework': 'application/javascript'
};

// Create HTTP server
const server = http.createServer((req, res) => {
    // Parse URL
    const parsedUrl = url.parse(req.url);
    let pathname = parsedUrl.pathname;
    
    // Remove leading slash and resolve path
    if (pathname === '/') {
        pathname = '/demo/index.html';
    }
    
    // Security: prevent directory traversal
    pathname = pathname.replace(/\.\./g, '');
    
    // Build file path
    const filePath = path.join(__dirname, pathname);
    
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url} -> ${filePath}`);
    
    // Check if file exists
    fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
            // File not found
            console.log(`File not found: ${filePath}`);
            res.writeHead(404, { 'Content-Type': 'text/html' });
            res.end(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>404 - Not Found</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                        h1 { color: #e74c3c; }
                        .back-link { color: #3498db; text-decoration: none; }
                    </style>
                </head>
                <body>
                    <h1>🚫 404 - File Not Found</h1>
                    <p>The requested file <code>${pathname}</code> was not found.</p>
                    <p><a href="/" class="back-link">← Back to AR Car Racing Demo</a></p>
                </body>
                </html>
            `);
            return;
        }
        
        // Get file extension and MIME type
        const ext = path.extname(filePath).toLowerCase();
        const mimeType = mimeTypes[ext] || 'application/octet-stream';
        
        // Set CORS headers for cross-origin requests
        const headers = {
            'Content-Type': mimeType,
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Cache-Control': 'no-cache'
        };
        
        // Special headers for WebAssembly and Unity files
        if (ext === '.wasm') {
            headers['Content-Type'] = 'application/wasm';
        } else if (ext === '.data') {
            headers['Content-Type'] = 'application/octet-stream';
        } else if (ext === '.framework') {
            headers['Content-Type'] = 'application/javascript';
        }
        
        // Handle OPTIONS requests (CORS preflight)
        if (req.method === 'OPTIONS') {
            res.writeHead(200, headers);
            res.end();
            return;
        }
        
        // Read and serve file
        fs.readFile(filePath, (err, data) => {
            if (err) {
                console.error(`Error reading file ${filePath}:`, err);
                res.writeHead(500, { 'Content-Type': 'text/html' });
                res.end(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>500 - Server Error</title>
                        <style>
                            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                            h1 { color: #e74c3c; }
                        </style>
                    </head>
                    <body>
                        <h1>🔥 500 - Server Error</h1>
                        <p>Error reading file: ${err.message}</p>
                    </body>
                    </html>
                `);
                return;
            }
            
            res.writeHead(200, headers);
            res.end(data);
        });
    });
});

// Start server
server.listen(PORT, HOST, () => {
    console.log('🚀 AR Car Racing Demo Server Started!');
    console.log('=====================================');
    console.log(`📡 Server running at: http://localhost:${PORT}`);
    console.log(`🌐 Network access: http://${getLocalIP()}:${PORT}`);
    console.log(`📱 Mobile access: http://${getLocalIP()}:${PORT}`);
    console.log('');
    console.log('🎮 Available endpoints:');
    console.log(`   • Demo: http://localhost:${PORT}/`);
    console.log(`   • Health: http://localhost:${PORT}/health`);
    console.log('');
    console.log('📱 For AR features, access from mobile device over HTTPS');
    console.log('🛑 Press Ctrl+C to stop the server');
    console.log('');
});

// Handle server errors
server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use. Try a different port:`);
        console.error(`   PORT=3001 node simple-server.js`);
    } else {
        console.error('❌ Server error:', err);
    }
    process.exit(1);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    server.close(() => {
        console.log('✅ Server stopped');
        process.exit(0);
    });
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
    server.close(() => {
        process.exit(0);
    });
});

// Utility function to get local IP
function getLocalIP() {
    const interfaces = require('os').networkInterfaces();
    for (const name of Object.keys(interfaces)) {
        for (const interface of interfaces[name]) {
            if (interface.family === 'IPv4' && !interface.internal) {
                return interface.address;
            }
        }
    }
    return 'localhost';
}

// Add health check endpoint
server.on('request', (req, res) => {
    if (req.url === '/health') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            status: 'ok',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            version: process.version
        }));
        return;
    }
});

console.log('🏎️ AR Car Racing - Simple Web Server');
console.log('====================================');
console.log('Starting server...');

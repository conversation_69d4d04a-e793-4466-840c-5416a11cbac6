using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using ARCarRacing.Car;
using ARCarRacing.Track;

namespace ARCarRacing.GameModes
{
    /// <summary>
    /// Time Trial game mode - players race against the clock to achieve the best lap time
    /// </summary>
    public class TimeTrialMode : GameModeBase
    {
        [Header("Time Trial Settings")]
        [SerializeField] private int totalLaps = 3;
        [SerializeField] private bool allowMultiplePlayers = true;
        [SerializeField] private float ghostCarDelay = 0.1f;
        
        [Header("Track")]
        [SerializeField] private List<TrackPiece> trackPieces = new List<TrackPiece>();
        [SerializeField] private Transform startPosition;
        [SerializeField] private bool requireTrackCompletion = true;
        
        // Time trial specific data
        private Dictionary<CarController, TimeTrialPlayerData> timeTrialData = new Dictionary<CarController, TimeTrialPlayerData>();
        private TrackPiece startFinishLine;
        
        // Ghost car system (for showing best lap)
        [SerializeField] private GameObject ghostCarPrefab;
        private List<GhostCarData> ghostCars = new List<GhostCarData>();
        
        // Time trial player data
        [System.Serializable]
        public class TimeTrialPlayerData
        {
            public float currentLapStartTime;
            public float currentLapTime;
            public List<float> lapTimes;
            public int currentLap;
            public int checkpointsHit;
            public bool isOnValidLap;
            
            public TimeTrialPlayerData()
            {
                currentLapStartTime = 0f;
                currentLapTime = 0f;
                lapTimes = new List<float>();
                currentLap = 0;
                checkpointsHit = 0;
                isOnValidLap = true;
            }
            
            public float GetBestLapTime()
            {
                if (lapTimes.Count == 0) return float.MaxValue;
                
                float best = float.MaxValue;
                foreach (float time in lapTimes)
                {
                    if (time < best) best = time;
                }
                return best;
            }
        }
        
        // Ghost car recording data
        [System.Serializable]
        public class GhostCarData
        {
            public List<Vector3> positions;
            public List<Quaternion> rotations;
            public List<float> timestamps;
            public float lapTime;
            
            public GhostCarData()
            {
                positions = new List<Vector3>();
                rotations = new List<Quaternion>();
                timestamps = new List<float>();
                lapTime = 0f;
            }
        }
        
        protected override void Awake()
        {
            base.Awake();
            
            gameModeName = "Time Trial";
            gameModeDescription = "Race against the clock to achieve the best lap time. Complete the required number of laps as fast as possible.";
            gameDuration = 0f; // No time limit for time trial
            maxPlayers = allowMultiplePlayers ? 4 : 1;
        }
        
        private void Start()
        {
            // Find track pieces and start/finish line
            FindTrackComponents();
        }
        
        private void FindTrackComponents()
        {
            // Find all track pieces in the scene
            TrackPiece[] foundPieces = FindObjectsOfType<TrackPiece>();
            trackPieces.AddRange(foundPieces);
            
            // Find start/finish line
            foreach (var piece in trackPieces)
            {
                if (piece.IsStartFinishLine)
                {
                    startFinishLine = piece;
                    startPosition = piece.transform;
                    break;
                }
            }
            
            // Subscribe to track events
            foreach (var piece in trackPieces)
            {
                piece.OnCarEntered += OnCarEnteredTrackPiece;
                piece.OnCheckpointReached += OnCheckpointReached;
            }
        }
        
        public override void StartGameMode()
        {
            base.StartGameMode();
            
            // Initialize time trial data for all players
            foreach (var player in players)
            {
                if (!timeTrialData.ContainsKey(player))
                {
                    timeTrialData[player] = new TimeTrialPlayerData();
                }
                
                StartPlayerLap(player);
            }
        }
        
        protected override void InitializePlayer(CarController player)
        {
            base.InitializePlayer(player);
            
            // Initialize time trial data
            if (!timeTrialData.ContainsKey(player))
            {
                timeTrialData[player] = new TimeTrialPlayerData();
            }
            
            // Reset time trial data
            TimeTrialPlayerData data = timeTrialData[player];
            data.currentLap = 0;
            data.currentLapTime = 0f;
            data.lapTimes.Clear();
            data.checkpointsHit = 0;
            data.isOnValidLap = true;
        }
        
        protected override void PositionPlayerAtStart(CarController player)
        {
            if (startPosition != null)
            {
                // Position players at start line with slight offsets
                int playerIndex = players.IndexOf(player);
                Vector3 offset = startPosition.right * (playerIndex * 2f - (players.Count - 1) * 1f);
                
                player.transform.position = startPosition.position + offset;
                player.transform.rotation = startPosition.rotation;
            }
        }
        
        protected override void UpdateGameMode()
        {
            base.UpdateGameMode();
            
            // Update current lap times for all players
            foreach (var player in players)
            {
                if (timeTrialData.ContainsKey(player))
                {
                    TimeTrialPlayerData data = timeTrialData[player];
                    if (data.currentLap > 0 && data.currentLap <= totalLaps)
                    {
                        data.currentLapTime = Time.time - data.currentLapStartTime;
                    }
                }
            }
            
            // Record ghost car data for best lap
            RecordGhostCarData();
        }
        
        private void StartPlayerLap(CarController player)
        {
            if (!timeTrialData.ContainsKey(player)) return;
            
            TimeTrialPlayerData data = timeTrialData[player];
            data.currentLap++;
            data.currentLapStartTime = Time.time;
            data.currentLapTime = 0f;
            data.checkpointsHit = 0;
            data.isOnValidLap = true;
            
            Debug.Log($"Player started lap {data.currentLap}/{totalLaps}");
        }
        
        private void FinishPlayerLap(CarController player)
        {
            if (!timeTrialData.ContainsKey(player)) return;
            
            TimeTrialPlayerData data = timeTrialData[player];
            
            if (!data.isOnValidLap)
            {
                Debug.Log("Invalid lap - player missed checkpoints");
                StartPlayerLap(player); // Restart the lap
                return;
            }
            
            float lapTime = Time.time - data.currentLapStartTime;
            data.lapTimes.Add(lapTime);
            
            // Update player game data
            if (playerData.ContainsKey(player))
            {
                UpdatePlayerLapTime(player, lapTime);
            }
            
            Debug.Log($"Player completed lap {data.currentLap} in {lapTime:F2} seconds");
            
            // Check if player finished all laps
            if (data.currentLap >= totalLaps)
            {
                FinishPlayer(player);
            }
            else
            {
                StartPlayerLap(player);
            }
        }
        
        private void FinishPlayer(CarController player)
        {
            if (!playerData.ContainsKey(player)) return;
            
            PlayerGameData gameData = playerData[player];
            gameData.isFinished = true;
            gameData.finishTime = GameTime;
            
            // Calculate total time
            TimeTrialPlayerData timeData = timeTrialData[player];
            float totalTime = 0f;
            foreach (float lapTime in timeData.lapTimes)
            {
                totalTime += lapTime;
            }
            
            Debug.Log($"Player finished time trial! Total time: {totalTime:F2}s, Best lap: {gameData.bestLapTime:F2}s");
            
            // Create ghost car from best lap
            CreateGhostCar(player);
        }
        
        protected override void CheckGameEndConditions()
        {
            base.CheckGameEndConditions();
            
            // Check if all players have finished
            bool allFinished = true;
            foreach (var player in players)
            {
                if (playerData.ContainsKey(player) && !playerData[player].isFinished)
                {
                    allFinished = false;
                    break;
                }
            }
            
            if (allFinished && players.Count > 0)
            {
                EndGameMode();
            }
        }
        
        private void OnCarEnteredTrackPiece(TrackPiece trackPiece, Collider carCollider)
        {
            CarController car = carCollider.GetComponent<CarController>();
            if (car == null || !players.Contains(car)) return;
            
            // Check if it's the start/finish line
            if (trackPiece == startFinishLine)
            {
                TimeTrialPlayerData data = timeTrialData[car];
                
                if (data.currentLap == 0)
                {
                    // Starting first lap
                    StartPlayerLap(car);
                }
                else if (data.currentLap <= totalLaps)
                {
                    // Finishing a lap
                    FinishPlayerLap(car);
                }
            }
        }
        
        private void OnCheckpointReached(TrackPiece trackPiece)
        {
            // Find which car reached the checkpoint
            List<Collider> carsOnTrack = trackPiece.GetCarsOnTrack();
            
            foreach (var carCollider in carsOnTrack)
            {
                CarController car = carCollider.GetComponent<CarController>();
                if (car != null && players.Contains(car) && timeTrialData.ContainsKey(car))
                {
                    TimeTrialPlayerData data = timeTrialData[car];
                    data.checkpointsHit++;
                }
            }
        }
        
        private void RecordGhostCarData()
        {
            // Record ghost car data for the player with the best current lap
            // This is a simplified implementation
            foreach (var player in players)
            {
                if (timeTrialData.ContainsKey(player))
                {
                    TimeTrialPlayerData data = timeTrialData[player];
                    if (data.currentLap > 0 && data.currentLap <= totalLaps)
                    {
                        // Record position and rotation for ghost car
                        // Implementation would store this data for playback
                    }
                }
            }
        }
        
        private void CreateGhostCar(CarController player)
        {
            if (ghostCarPrefab == null) return;
            
            // Create ghost car that replays the best lap
            // This is a placeholder for ghost car implementation
            Debug.Log($"Creating ghost car for player's best lap");
        }
        
        public TimeTrialPlayerData GetTimeTrialData(CarController player)
        {
            return timeTrialData.ContainsKey(player) ? timeTrialData[player] : null;
        }
        
        public float GetCurrentLapTime(CarController player)
        {
            TimeTrialPlayerData data = GetTimeTrialData(player);
            return data?.currentLapTime ?? 0f;
        }
        
        public int GetCurrentLap(CarController player)
        {
            TimeTrialPlayerData data = GetTimeTrialData(player);
            return data?.currentLap ?? 0;
        }
        
        public List<float> GetLapTimes(CarController player)
        {
            TimeTrialPlayerData data = GetTimeTrialData(player);
            return data?.lapTimes ?? new List<float>();
        }
    }
}

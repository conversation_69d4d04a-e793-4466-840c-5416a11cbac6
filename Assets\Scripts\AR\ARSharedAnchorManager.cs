using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.XR.ARFoundation;
using UnityEngine.XR.ARSubsystems;
using Photon.Pun;

namespace ARCarRacing.AR
{
    /// <summary>
    /// Manages shared AR anchors for multiplayer synchronization
    /// </summary>
    public class ARSharedAnchorManager : MonoBehaviourPunPV, IPunObservable
    {
        [Header("Anchor Settings")]
        [SerializeField] private bool enableAnchorSharing = true;
        [SerializeField] private float anchorSyncInterval = 1f;
        [SerializeField] private int maxSharedAnchors = 10;
        
        [Header("World Origin")]
        [SerializeField] private Transform worldOrigin;
        [SerializeField] private bool useWorldOriginAnchor = true;
        
        // Components
        private ARAnchorManager anchorManager;
        private ARManager arManager;
        
        // Shared anchors
        private Dictionary<string, ARAnchor> sharedAnchors = new Dictionary<string, ARAnchor>();
        private Dictionary<string, SharedAnchorData> anchorDataCache = new Dictionary<string, SharedAnchorData>();
        
        // World synchronization
        public bool IsWorldSynced { get; private set; } = false;
        public Vector3 WorldOriginPosition { get; private set; }
        public Quaternion WorldOriginRotation { get; private set; }
        
        // Events
        public System.Action OnWorldSynced;
        public System.Action<string, Vector3, Quaternion> OnSharedAnchorAdded;
        public System.Action<string> OnSharedAnchorRemoved;
        public System.Action OnAnchorSharingFailed;
        
        // Data structures
        [System.Serializable]
        public class SharedAnchorData
        {
            public string anchorId;
            public Vector3 position;
            public Quaternion rotation;
            public float timestamp;
            public int ownerId;
            
            public SharedAnchorData(string id, Vector3 pos, Quaternion rot, int owner)
            {
                anchorId = id;
                position = pos;
                rotation = rot;
                timestamp = Time.time;
                ownerId = owner;
            }
        }
        
        private void Awake()
        {
            // Get required components
            anchorManager = FindObjectOfType<ARAnchorManager>();
            arManager = FindObjectOfType<ARManager>();
            
            if (worldOrigin == null)
            {
                // Create world origin if not assigned
                GameObject originGO = new GameObject("WorldOrigin");
                worldOrigin = originGO.transform;
            }
        }
        
        private void Start()
        {
            // Subscribe to AR events
            if (arManager != null)
            {
                arManager.OnTrackPlaced += OnTrackPlaced;
            }
            
            // Initialize world origin
            if (useWorldOriginAnchor && PhotonNetwork.IsMasterClient)
            {
                StartCoroutine(InitializeWorldOrigin());
            }
        }
        
        private IEnumerator InitializeWorldOrigin()
        {
            // Wait for AR to be ready
            while (!ARManager.Instance.IsARReady)
            {
                yield return null;
            }
            
            // Create world origin anchor
            yield return new WaitForSeconds(1f); // Give AR time to stabilize
            
            CreateWorldOriginAnchor();
        }
        
        private void CreateWorldOriginAnchor()
        {
            if (anchorManager == null) return;
            
            // Create anchor at current camera position
            Camera arCamera = Camera.main;
            if (arCamera == null) return;
            
            Vector3 originPosition = arCamera.transform.position;
            originPosition.y = 0; // Place on ground level
            
            Quaternion originRotation = Quaternion.identity;
            
            // Create the anchor
            ARAnchor worldAnchor = anchorManager.AddAnchor(new Pose(originPosition, originRotation));
            
            if (worldAnchor != null)
            {
                worldOrigin.position = originPosition;
                worldOrigin.rotation = originRotation;
                
                WorldOriginPosition = originPosition;
                WorldOriginRotation = originRotation;
                
                // Share world origin with other players
                ShareWorldOrigin(originPosition, originRotation);
                
                IsWorldSynced = true;
                OnWorldSynced?.Invoke();
                
                Debug.Log($"World origin anchor created at: {originPosition}");
            }
            else
            {
                Debug.LogError("Failed to create world origin anchor");
                OnAnchorSharingFailed?.Invoke();
            }
        }
        
        private void ShareWorldOrigin(Vector3 position, Quaternion rotation)
        {
            if (!PhotonNetwork.IsMasterClient) return;
            
            // Send world origin to all players
            photonView.RPC("ReceiveWorldOrigin", RpcTarget.Others, 
                position.x, position.y, position.z,
                rotation.x, rotation.y, rotation.z, rotation.w);
        }
        
        [PunRPC]
        private void ReceiveWorldOrigin(float px, float py, float pz, float rx, float ry, float rz, float rw)
        {
            Vector3 position = new Vector3(px, py, pz);
            Quaternion rotation = new Quaternion(rx, ry, rz, rw);
            
            // Set world origin
            worldOrigin.position = position;
            worldOrigin.rotation = rotation;
            
            WorldOriginPosition = position;
            WorldOriginRotation = rotation;
            
            IsWorldSynced = true;
            OnWorldSynced?.Invoke();
            
            Debug.Log($"Received world origin: {position}");
        }
        
        public string CreateSharedAnchor(Vector3 position, Quaternion rotation)
        {
            if (anchorManager == null || !enableAnchorSharing) return null;
            
            // Generate unique anchor ID
            string anchorId = System.Guid.NewGuid().ToString();
            
            // Create local anchor
            ARAnchor anchor = anchorManager.AddAnchor(new Pose(position, rotation));
            
            if (anchor != null)
            {
                sharedAnchors[anchorId] = anchor;
                
                // Create anchor data
                SharedAnchorData anchorData = new SharedAnchorData(anchorId, position, rotation, PhotonNetwork.LocalPlayer.ActorNumber);
                anchorDataCache[anchorId] = anchorData;
                
                // Share with other players
                ShareAnchorWithOthers(anchorData);
                
                OnSharedAnchorAdded?.Invoke(anchorId, position, rotation);
                
                Debug.Log($"Created shared anchor: {anchorId} at {position}");
                
                return anchorId;
            }
            
            return null;
        }
        
        private void ShareAnchorWithOthers(SharedAnchorData anchorData)
        {
            if (!PhotonNetwork.InRoom) return;
            
            // Send anchor data to other players
            photonView.RPC("ReceiveSharedAnchor", RpcTarget.Others,
                anchorData.anchorId,
                anchorData.position.x, anchorData.position.y, anchorData.position.z,
                anchorData.rotation.x, anchorData.rotation.y, anchorData.rotation.z, anchorData.rotation.w,
                anchorData.ownerId);
        }
        
        [PunRPC]
        private void ReceiveSharedAnchor(string anchorId, float px, float py, float pz, 
                                       float rx, float ry, float rz, float rw, int ownerId)
        {
            Vector3 position = new Vector3(px, py, pz);
            Quaternion rotation = new Quaternion(rx, ry, rz, rw);
            
            // Create local anchor for received data
            if (anchorManager != null)
            {
                ARAnchor anchor = anchorManager.AddAnchor(new Pose(position, rotation));
                
                if (anchor != null)
                {
                    sharedAnchors[anchorId] = anchor;
                    
                    SharedAnchorData anchorData = new SharedAnchorData(anchorId, position, rotation, ownerId);
                    anchorDataCache[anchorId] = anchorData;
                    
                    OnSharedAnchorAdded?.Invoke(anchorId, position, rotation);
                    
                    Debug.Log($"Received shared anchor: {anchorId} from player {ownerId}");
                }
            }
        }
        
        public void RemoveSharedAnchor(string anchorId)
        {
            if (sharedAnchors.ContainsKey(anchorId))
            {
                // Remove local anchor
                ARAnchor anchor = sharedAnchors[anchorId];
                if (anchor != null && anchorManager != null)
                {
                    anchorManager.RemoveAnchor(anchor);
                }
                
                sharedAnchors.Remove(anchorId);
                anchorDataCache.Remove(anchorId);
                
                // Notify other players
                if (PhotonNetwork.InRoom)
                {
                    photonView.RPC("RemoveSharedAnchorRPC", RpcTarget.Others, anchorId);
                }
                
                OnSharedAnchorRemoved?.Invoke(anchorId);
                
                Debug.Log($"Removed shared anchor: {anchorId}");
            }
        }
        
        [PunRPC]
        private void RemoveSharedAnchorRPC(string anchorId)
        {
            RemoveSharedAnchor(anchorId);
        }
        
        public Vector3 ConvertToWorldSpace(Vector3 localPosition)
        {
            if (!IsWorldSynced) return localPosition;
            
            return worldOrigin.TransformPoint(localPosition);
        }
        
        public Vector3 ConvertToLocalSpace(Vector3 worldPosition)
        {
            if (!IsWorldSynced) return worldPosition;
            
            return worldOrigin.InverseTransformPoint(worldPosition);
        }
        
        public ARAnchor GetSharedAnchor(string anchorId)
        {
            return sharedAnchors.ContainsKey(anchorId) ? sharedAnchors[anchorId] : null;
        }
        
        public List<string> GetSharedAnchorIds()
        {
            return new List<string>(sharedAnchors.Keys);
        }
        
        private void OnTrackPlaced(Vector3 position, Quaternion rotation)
        {
            // Automatically create shared anchor when track is placed
            if (enableAnchorSharing && PhotonNetwork.InRoom)
            {
                CreateSharedAnchor(position, rotation);
            }
        }
        
        public void OnPhotonSerializeView(PhotonStream stream, PhotonMessageInfo info)
        {
            if (stream.IsWriting)
            {
                // Send world origin data
                stream.SendNext(IsWorldSynced);
                if (IsWorldSynced)
                {
                    stream.SendNext(WorldOriginPosition);
                    stream.SendNext(WorldOriginRotation);
                }
            }
            else
            {
                // Receive world origin data
                bool synced = (bool)stream.ReceiveNext();
                if (synced && !IsWorldSynced)
                {
                    WorldOriginPosition = (Vector3)stream.ReceiveNext();
                    WorldOriginRotation = (Quaternion)stream.ReceiveNext();
                    
                    worldOrigin.position = WorldOriginPosition;
                    worldOrigin.rotation = WorldOriginRotation;
                    
                    IsWorldSynced = true;
                    OnWorldSynced?.Invoke();
                }
            }
        }
        
        private void OnDestroy()
        {
            // Clean up anchors
            foreach (var anchor in sharedAnchors.Values)
            {
                if (anchor != null && anchorManager != null)
                {
                    anchorManager.RemoveAnchor(anchor);
                }
            }
            
            sharedAnchors.Clear();
            anchorDataCache.Clear();
        }
    }
}

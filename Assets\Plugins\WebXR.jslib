// WebXR JavaScript plugin for Unity WebGL
var WebXRPlugin = {
    // WebXR session variables
    xrSession: null,
    xrReferenceSpace: null,
    gl: null,
    xrFramebuffer: null,
    isSessionRunning: false,
    
    // Unity callback object name
    unityObjectName: "WebXRManager",
    
    // Check if WebXR is supported
    IsWebXRSupported: function() {
        if (typeof navigator !== 'undefined' && navigator.xr) {
            return navigator.xr.isSessionSupported('immersive-ar')
                .then(function(supported) {
                    return supported;
                })
                .catch(function() {
                    return false;
                });
        }
        return false;
    },
    
    // Start AR session
    StartARSession: function() {
        if (typeof navigator === 'undefined' || !navigator.xr) {
            console.error('WebXR not supported');
            return;
        }
        
        navigator.xr.requestSession('immersive-ar', {
            requiredFeatures: ['hit-test'],
            optionalFeatures: ['dom-overlay'],
            domOverlay: { root: document.body }
        }).then(function(session) {
            WebXRPlugin.xrSession = session;
            WebXRPlugin.isSessionRunning = true;
            
            // Set up session
            WebXRPlugin.setupSession(session);
            
            // Notify Unity
            if (typeof unityInstance !== 'undefined') {
                unityInstance.SendMessage(WebXRPlugin.unityObjectName, 'OnWebXRSessionStarted', '');
            }
            
            console.log('AR session started');
        }).catch(function(error) {
            console.error('Failed to start AR session:', error);
            
            // Notify Unity of error
            if (typeof unityInstance !== 'undefined') {
                unityInstance.SendMessage(WebXRPlugin.unityObjectName, 'OnWebXRError', error.toString());
            }
        });
    },
    
    // End AR session
    EndARSession: function() {
        if (WebXRPlugin.xrSession) {
            WebXRPlugin.xrSession.end();
            WebXRPlugin.xrSession = null;
            WebXRPlugin.isSessionRunning = false;
            
            // Notify Unity
            if (typeof unityInstance !== 'undefined') {
                unityInstance.SendMessage(WebXRPlugin.unityObjectName, 'OnWebXRSessionEnded', '');
            }
            
            console.log('AR session ended');
        }
    },
    
    // Check if AR session is running
    IsARSessionRunning: function() {
        return WebXRPlugin.isSessionRunning;
    },
    
    // Setup WebXR session
    setupSession: function(session) {
        session.addEventListener('end', function() {
            WebXRPlugin.isSessionRunning = false;
            
            // Notify Unity
            if (typeof unityInstance !== 'undefined') {
                unityInstance.SendMessage(WebXRPlugin.unityObjectName, 'OnWebXRSessionEnded', '');
            }
        });
        
        // Get reference space
        session.requestReferenceSpace('local-floor').then(function(refSpace) {
            WebXRPlugin.xrReferenceSpace = refSpace;
            
            // Start render loop
            session.requestAnimationFrame(WebXRPlugin.onXRFrame);
        });
    },
    
    // XR frame callback
    onXRFrame: function(time, frame) {
        if (!WebXRPlugin.xrSession) return;
        
        // Continue the render loop
        WebXRPlugin.xrSession.requestAnimationFrame(WebXRPlugin.onXRFrame);
        
        // Get viewer pose
        let pose = frame.getViewerPose(WebXRPlugin.xrReferenceSpace);
        if (pose) {
            // Update camera position in Unity if needed
            // This would require more complex integration
        }
    },
    
    // Request hit test
    RequestHitTest: function(screenX, screenY) {
        if (!WebXRPlugin.xrSession || !WebXRPlugin.xrReferenceSpace) {
            console.warn('XR session not active');
            return;
        }
        
        // Convert screen coordinates to normalized device coordinates
        let canvas = document.querySelector('canvas');
        if (!canvas) return;
        
        let rect = canvas.getBoundingClientRect();
        let x = ((screenX - rect.left) / rect.width) * 2 - 1;
        let y = (1 - (screenY - rect.top) / rect.height) * 2 - 1;
        
        // Create ray for hit testing
        WebXRPlugin.xrSession.requestAnimationFrame(function(time, frame) {
            // Create hit test source
            WebXRPlugin.xrSession.requestHitTestSource({
                space: WebXRPlugin.xrReferenceSpace
            }).then(function(hitTestSource) {
                // Perform hit test
                let hitTestResults = frame.getHitTestResults(hitTestSource);
                
                if (hitTestResults.length > 0) {
                    let hit = hitTestResults[0];
                    let pose = hit.getPose(WebXRPlugin.xrReferenceSpace);
                    
                    if (pose) {
                        let position = pose.transform.position;
                        let positionString = position.x + ',' + position.y + ',' + position.z;
                        
                        // Send result to Unity
                        if (typeof unityInstance !== 'undefined') {
                            unityInstance.SendMessage(WebXRPlugin.unityObjectName, 'OnHitTestResult', positionString);
                        }
                    }
                }
                
                // Clean up hit test source
                hitTestSource.cancel();
            }).catch(function(error) {
                console.error('Hit test failed:', error);
            });
        });
    },
    
    // Place object (placeholder for future use)
    PlaceObject: function(x, y, z) {
        console.log('Placing object at:', x, y, z);
        // This could be used to place objects in the AR scene
    },
    
    // Utility function to check WebXR support with more detail
    CheckWebXRSupport: function() {
        if (typeof navigator === 'undefined' || !navigator.xr) {
            return 'WebXR not supported';
        }
        
        return navigator.xr.isSessionSupported('immersive-ar')
            .then(function(supported) {
                if (supported) {
                    return 'WebXR AR supported';
                } else {
                    return 'WebXR supported but AR not available';
                }
            })
            .catch(function(error) {
                return 'Error checking WebXR support: ' + error;
            });
    },
    
    // Initialize WebXR (call this when page loads)
    InitializeWebXR: function() {
        console.log('Initializing WebXR...');
        
        // Check for HTTPS (required for WebXR)
        if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
            console.warn('WebXR requires HTTPS or localhost');
            return false;
        }
        
        // Check WebXR support
        if (typeof navigator !== 'undefined' && navigator.xr) {
            navigator.xr.isSessionSupported('immersive-ar').then(function(supported) {
                console.log('WebXR AR supported:', supported);
                
                if (supported) {
                    // Add AR button or UI elements
                    WebXRPlugin.createARButton();
                }
            });
            
            return true;
        }
        
        console.log('WebXR not available');
        return false;
    },
    
    // Create AR button in the page
    createARButton: function() {
        // Check if button already exists
        if (document.getElementById('ar-button')) return;
        
        let button = document.createElement('button');
        button.id = 'ar-button';
        button.textContent = 'Start AR';
        button.style.position = 'fixed';
        button.style.bottom = '20px';
        button.style.left = '50%';
        button.style.transform = 'translateX(-50%)';
        button.style.padding = '10px 20px';
        button.style.fontSize = '16px';
        button.style.backgroundColor = '#007bff';
        button.style.color = 'white';
        button.style.border = 'none';
        button.style.borderRadius = '5px';
        button.style.cursor = 'pointer';
        button.style.zIndex = '1000';
        
        button.addEventListener('click', function() {
            WebXRPlugin.StartARSession();
        });
        
        document.body.appendChild(button);
    },
    
    // Remove AR button
    removeARButton: function() {
        let button = document.getElementById('ar-button');
        if (button) {
            button.remove();
        }
    }
};

// Auto-initialize when script loads
if (typeof document !== 'undefined') {
    document.addEventListener('DOMContentLoaded', function() {
        WebXRPlugin.InitializeWebXR();
    });
}

// Export functions for Unity
mergeInto(LibraryManager.library, WebXRPlugin);

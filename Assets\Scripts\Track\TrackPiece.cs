using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace ARCarRacing.Track
{
    /// <summary>
    /// Represents a single piece of the racing track
    /// </summary>
    public class TrackPiece : MonoBehaviour
    {
        [Header("Track Settings")]
        [SerializeField] private TrackPieceType pieceType = TrackPieceType.Straight;
        [SerializeField] private float trackWidth = 2f;
        [SerializeField] private float pieceLength = 5f;
        [SerializeField] private bool isStartFinishLine = false;
        
        [Header("Connection Points")]
        [SerializeField] private Transform entryPoint;
        [SerializeField] private Transform exitPoint;
        [SerializeField] private bool canConnectToNext = true;
        [SerializeField] private bool canConnectToPrevious = true;
        
        [Header("Visual")]
        [SerializeField] private MeshRenderer trackRenderer;
        [SerializeField] private Material trackMaterial;
        [SerializeField] private Material startFinishMaterial;
        [SerializeField] private LineRenderer trackBoundary;
        
        [Header("Checkpoints")]
        [SerializeField] private Transform[] checkpoints;
        [SerializeField] private bool hasCheckpoint = false;
        
        // Track piece types
        public enum TrackPieceType
        {
            Straight,
            CurveLeft,
            CurveRight,
            SharpLeft,
            SharpRight,
            StartFinish,
            Jump,
            Boost
        }
        
        // Properties
        public TrackPieceType PieceType => pieceType;
        public float TrackWidth => trackWidth;
        public float PieceLength => pieceLength;
        public bool IsStartFinishLine => isStartFinishLine;
        public Transform EntryPoint => entryPoint;
        public Transform ExitPoint => exitPoint;
        public bool HasCheckpoint => hasCheckpoint;
        public Transform[] Checkpoints => checkpoints;
        
        // Events
        public System.Action<TrackPiece, Collider> OnCarEntered;
        public System.Action<TrackPiece, Collider> OnCarExited;
        public System.Action<TrackPiece> OnCheckpointReached;
        
        // Private variables
        private List<Collider> carsOnTrack = new List<Collider>();
        private bool isInitialized = false;
        
        private void Awake()
        {
            InitializeTrackPiece();
        }
        
        private void Start()
        {
            SetupVisuals();
            SetupColliders();
        }
        
        private void InitializeTrackPiece()
        {
            // Create connection points if they don't exist
            if (entryPoint == null)
            {
                GameObject entryGO = new GameObject("EntryPoint");
                entryGO.transform.SetParent(transform);
                entryGO.transform.localPosition = new Vector3(0, 0, -pieceLength * 0.5f);
                entryPoint = entryGO.transform;
            }
            
            if (exitPoint == null)
            {
                GameObject exitGO = new GameObject("ExitPoint");
                exitGO.transform.SetParent(transform);
                exitGO.transform.localPosition = new Vector3(0, 0, pieceLength * 0.5f);
                exitPoint = exitGO.transform;
            }
            
            // Get track renderer if not assigned
            if (trackRenderer == null)
            {
                trackRenderer = GetComponent<MeshRenderer>();
            }
            
            isInitialized = true;
        }
        
        private void SetupVisuals()
        {
            // Apply appropriate material based on track piece type
            if (trackRenderer != null)
            {
                if (isStartFinishLine && startFinishMaterial != null)
                {
                    trackRenderer.material = startFinishMaterial;
                }
                else if (trackMaterial != null)
                {
                    trackRenderer.material = trackMaterial;
                }
            }
            
            // Setup track boundary visualization
            if (trackBoundary != null)
            {
                SetupTrackBoundary();
            }
        }
        
        private void SetupTrackBoundary()
        {
            // Create boundary lines for the track
            trackBoundary.positionCount = 4;
            trackBoundary.useWorldSpace = false;
            trackBoundary.loop = true;
            
            float halfWidth = trackWidth * 0.5f;
            float halfLength = pieceLength * 0.5f;
            
            Vector3[] boundaryPoints = new Vector3[]
            {
                new Vector3(-halfWidth, 0.01f, -halfLength),
                new Vector3(halfWidth, 0.01f, -halfLength),
                new Vector3(halfWidth, 0.01f, halfLength),
                new Vector3(-halfWidth, 0.01f, halfLength)
            };
            
            trackBoundary.SetPositions(boundaryPoints);
        }
        
        private void SetupColliders()
        {
            // Ensure we have a collider for car detection
            Collider trackCollider = GetComponent<Collider>();
            if (trackCollider == null)
            {
                BoxCollider boxCollider = gameObject.AddComponent<BoxCollider>();
                boxCollider.size = new Vector3(trackWidth, 0.1f, pieceLength);
                boxCollider.isTrigger = true;
            }
            else
            {
                trackCollider.isTrigger = true;
            }
        }
        
        public void ConnectToNext(TrackPiece nextPiece)
        {
            if (nextPiece == null || !canConnectToNext || !nextPiece.canConnectToPrevious)
                return;
            
            // Align the next piece's entry point with this piece's exit point
            Vector3 offset = exitPoint.position - nextPiece.entryPoint.position;
            nextPiece.transform.position += offset;
            
            // Align rotation
            float angleDifference = exitPoint.eulerAngles.y - nextPiece.entryPoint.eulerAngles.y;
            nextPiece.transform.Rotate(0, angleDifference, 0);
        }
        
        public Vector3 GetPositionOnTrack(float normalizedPosition)
        {
            // Get a position along the track (0 = entry, 1 = exit)
            normalizedPosition = Mathf.Clamp01(normalizedPosition);
            
            Vector3 startPos = entryPoint.position;
            Vector3 endPos = exitPoint.position;
            
            return Vector3.Lerp(startPos, endPos, normalizedPosition);
        }
        
        public Vector3 GetDirectionOnTrack(float normalizedPosition)
        {
            // Get the direction along the track at a given position
            Vector3 direction = (exitPoint.position - entryPoint.position).normalized;
            
            // For curved pieces, we might want to interpolate the direction
            if (pieceType == TrackPieceType.CurveLeft || pieceType == TrackPieceType.CurveRight)
            {
                // Simple curve direction calculation
                float angle = (pieceType == TrackPieceType.CurveLeft ? -45f : 45f) * normalizedPosition;
                direction = Quaternion.Euler(0, angle, 0) * direction;
            }
            
            return direction;
        }
        
        public bool IsCarOnTrack(Collider carCollider)
        {
            return carsOnTrack.Contains(carCollider);
        }
        
        public List<Collider> GetCarsOnTrack()
        {
            return new List<Collider>(carsOnTrack);
        }
        
        private void OnTriggerEnter(Collider other)
        {
            // Check if it's a car
            if (other.CompareTag("Car") || other.GetComponent<Car.CarController>() != null)
            {
                if (!carsOnTrack.Contains(other))
                {
                    carsOnTrack.Add(other);
                    OnCarEntered?.Invoke(this, other);
                    
                    // Check for checkpoint
                    if (hasCheckpoint)
                    {
                        OnCheckpointReached?.Invoke(this);
                    }
                }
            }
        }
        
        private void OnTriggerExit(Collider other)
        {
            // Check if it's a car
            if (other.CompareTag("Car") || other.GetComponent<Car.CarController>() != null)
            {
                if (carsOnTrack.Contains(other))
                {
                    carsOnTrack.Remove(other);
                    OnCarExited?.Invoke(this, other);
                }
            }
        }
        
        public void SetTrackPieceType(TrackPieceType newType)
        {
            pieceType = newType;
            
            // Update visual appearance based on type
            if (newType == TrackPieceType.StartFinish)
            {
                isStartFinishLine = true;
                hasCheckpoint = true;
            }
            
            SetupVisuals();
        }
        
        public void SetAsStartFinish(bool isStart)
        {
            isStartFinishLine = isStart;
            hasCheckpoint = isStart;
            
            if (isStart)
            {
                pieceType = TrackPieceType.StartFinish;
            }
            
            SetupVisuals();
        }
        
        public void HighlightTrackPiece(bool highlight, Color highlightColor = default)
        {
            if (trackRenderer == null) return;
            
            if (highlight)
            {
                if (highlightColor == default)
                    highlightColor = Color.yellow;
                
                trackRenderer.material.color = highlightColor;
            }
            else
            {
                // Reset to original color
                trackRenderer.material.color = Color.white;
            }
        }
        
        private void OnDrawGizmosSelected()
        {
            if (!isInitialized) return;
            
            // Draw connection points
            Gizmos.color = Color.green;
            if (entryPoint != null)
            {
                Gizmos.DrawWireSphere(entryPoint.position, 0.2f);
                Gizmos.DrawRay(entryPoint.position, entryPoint.forward * 0.5f);
            }
            
            Gizmos.color = Color.red;
            if (exitPoint != null)
            {
                Gizmos.DrawWireSphere(exitPoint.position, 0.2f);
                Gizmos.DrawRay(exitPoint.position, exitPoint.forward * 0.5f);
            }
            
            // Draw track bounds
            Gizmos.color = Color.white;
            Gizmos.DrawWireCube(transform.position, new Vector3(trackWidth, 0.1f, pieceLength));
            
            // Draw checkpoints
            if (hasCheckpoint && checkpoints != null)
            {
                Gizmos.color = Color.cyan;
                foreach (var checkpoint in checkpoints)
                {
                    if (checkpoint != null)
                    {
                        Gizmos.DrawWireSphere(checkpoint.position, 0.3f);
                    }
                }
            }
        }
    }
}

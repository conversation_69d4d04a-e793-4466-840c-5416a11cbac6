using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace ARCarRacing.Managers
{
    /// <summary>
    /// Main game manager that handles overall game state and flow
    /// </summary>
    public class GameManager : MonoBehaviour
    {
        [Header("Game Settings")]
        [SerializeField] private bool debugMode = false;
        [SerializeField] private float gameTimeScale = 1.0f;
        
        [Header("Scene Management")]
        [SerializeField] private string mainMenuScene = "MainMenu";
        [SerializeField] private string gameplayScene = "Gameplay";
        [SerializeField] private string lobbyScene = "Lobby";
        
        // Singleton instance
        public static GameManager Instance { get; private set; }
        
        // Game state
        public enum GameState
        {
            MainMenu,
            Lobby,
            Gameplay,
            Paused,
            GameOver,
            Loading
        }
        
        [SerializeField] private GameState currentState = GameState.MainMenu;
        public GameState CurrentState => currentState;
        
        // Events
        public System.Action<GameState> OnGameStateChanged;
        public System.Action OnGameStarted;
        public System.Action OnGameEnded;
        public System.Action OnGamePaused;
        public System.Action OnGameResumed;
        
        // Game session data
        public int CurrentLap { get; private set; } = 0;
        public float RaceTime { get; private set; } = 0f;
        public bool IsRaceActive { get; private set; } = false;
        
        private void Awake()
        {
            // Singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeGame();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            // Set initial game state
            ChangeGameState(GameState.MainMenu);
        }
        
        private void Update()
        {
            // Update race time if race is active
            if (IsRaceActive && currentState == GameState.Gameplay)
            {
                RaceTime += Time.deltaTime;
            }
            
            // Handle back button on Android
            if (Application.platform == RuntimePlatform.Android)
            {
                if (Input.GetKeyDown(KeyCode.Escape))
                {
                    HandleBackButton();
                }
            }
        }
        
        private void InitializeGame()
        {
            // Set target frame rate for mobile
            Application.targetFrameRate = 60;
            
            // Keep screen on during gameplay
            Screen.sleepTimeout = SleepTimeout.NeverSleep;
            
            // Set time scale
            Time.timeScale = gameTimeScale;
            
            if (debugMode)
            {
                Debug.Log("GameManager initialized in debug mode");
            }
        }
        
        public void ChangeGameState(GameState newState)
        {
            if (currentState == newState) return;
            
            GameState previousState = currentState;
            currentState = newState;
            
            if (debugMode)
            {
                Debug.Log($"Game state changed from {previousState} to {newState}");
            }
            
            OnGameStateChanged?.Invoke(newState);
            
            // Handle state-specific logic
            switch (newState)
            {
                case GameState.Gameplay:
                    StartRace();
                    break;
                case GameState.Paused:
                    PauseGame();
                    break;
                case GameState.GameOver:
                    EndRace();
                    break;
            }
        }
        
        public void StartRace()
        {
            IsRaceActive = true;
            RaceTime = 0f;
            CurrentLap = 0;
            OnGameStarted?.Invoke();
            
            if (debugMode)
            {
                Debug.Log("Race started");
            }
        }
        
        public void EndRace()
        {
            IsRaceActive = false;
            OnGameEnded?.Invoke();
            
            if (debugMode)
            {
                Debug.Log($"Race ended. Final time: {RaceTime:F2}s, Laps: {CurrentLap}");
            }
        }
        
        public void PauseGame()
        {
            Time.timeScale = 0f;
            OnGamePaused?.Invoke();
            
            if (debugMode)
            {
                Debug.Log("Game paused");
            }
        }
        
        public void ResumeGame()
        {
            Time.timeScale = gameTimeScale;
            ChangeGameState(GameState.Gameplay);
            OnGameResumed?.Invoke();
            
            if (debugMode)
            {
                Debug.Log("Game resumed");
            }
        }
        
        public void CompleteLap()
        {
            CurrentLap++;
            
            if (debugMode)
            {
                Debug.Log($"Lap {CurrentLap} completed at time {RaceTime:F2}s");
            }
        }
        
        public void LoadScene(string sceneName)
        {
            ChangeGameState(GameState.Loading);
            StartCoroutine(LoadSceneAsync(sceneName));
        }
        
        private IEnumerator LoadSceneAsync(string sceneName)
        {
            AsyncOperation asyncLoad = SceneManager.LoadSceneAsync(sceneName);
            
            while (!asyncLoad.isDone)
            {
                yield return null;
            }
            
            // Determine new game state based on loaded scene
            if (sceneName == mainMenuScene)
            {
                ChangeGameState(GameState.MainMenu);
            }
            else if (sceneName == lobbyScene)
            {
                ChangeGameState(GameState.Lobby);
            }
            else if (sceneName == gameplayScene)
            {
                ChangeGameState(GameState.Gameplay);
            }
        }
        
        private void HandleBackButton()
        {
            switch (currentState)
            {
                case GameState.Gameplay:
                    ChangeGameState(GameState.Paused);
                    break;
                case GameState.Paused:
                    ResumeGame();
                    break;
                case GameState.Lobby:
                    LoadScene(mainMenuScene);
                    break;
                default:
                    // In main menu, quit application
                    QuitGame();
                    break;
            }
        }
        
        public void QuitGame()
        {
            if (debugMode)
            {
                Debug.Log("Quitting game");
            }
            
            #if UNITY_EDITOR
                UnityEditor.EditorApplication.isPlaying = false;
            #else
                Application.Quit();
            #endif
        }
        
        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus && currentState == GameState.Gameplay)
            {
                ChangeGameState(GameState.Paused);
            }
        }
        
        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus && currentState == GameState.Gameplay)
            {
                ChangeGameState(GameState.Paused);
            }
        }
    }
}

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using ARCarRacing.Managers;

namespace ARCarRacing.UI
{
    /// <summary>
    /// Main UI manager for the AR car racing game
    /// </summary>
    public class UIManager : MonoBehaviour
    {
        [Header("UI Panels")]
        [SerializeField] private GameObject mainMenuPanel;
        [SerializeField] private GameObject gameplayPanel;
        [SerializeField] private GameObject lobbyPanel;
        [SerializeField] private GameObject pausePanel;
        [SerializeField] private GameObject settingsPanel;
        [SerializeField] private GameObject loadingPanel;
        
        [Header("Gameplay UI")]
        [SerializeField] private TextMeshProUGUI speedText;
        [SerializeField] private TextMeshProUGUI lapTimeText;
        [SerializeField] private TextMeshProUGUI lapCountText;
        [SerializeField] private TextMeshProUGUI positionText;
        [SerializeField] private Slider speedometer;
        [Serial<PERSON><PERSON>ield] private Image minimap;
        
        [Header("AR UI")]
        [SerializeField] private Button placementButton;
        [SerializeField] private TextMeshPro<PERSON>G<PERSON> arStatusText;
        [SerializeField] private GameObject placementInstructions;
        [SerializeField] private Button resetTrackButton;
        
        [Header("Multiplayer UI")]
        [SerializeField] private TextMeshProUGUI playerCountText;
        [SerializeField] private TextMeshProUGUI roomNameText;
        [SerializeField] private Button leaveRoomButton;
        [SerializeField] private Transform playerListParent;
        [SerializeField] private GameObject playerListItemPrefab;
        
        [Header("Controls UI")]
        [SerializeField] private RectTransform throttleArea;
        [SerializeField] private RectTransform steerArea;
        [SerializeField] private Button brakeButton;
        [SerializeField] private Slider sensitivitySlider;
        
        // Singleton instance
        public static UIManager Instance { get; private set; }
        
        // UI state
        private Dictionary<string, GameObject> uiPanels;
        private string currentPanel = "";
        
        // Player list items
        private List<GameObject> playerListItems = new List<GameObject>();
        
        // Events
        public System.Action OnPlacementButtonClicked;
        public System.Action OnResetTrackButtonClicked;
        public System.Action<float> OnSensitivityChanged;
        
        private void Awake()
        {
            // Singleton pattern
            if (Instance == null)
            {
                Instance = this;
                InitializeUI();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            // Subscribe to game manager events
            if (GameManager.Instance != null)
            {
                GameManager.Instance.OnGameStateChanged += OnGameStateChanged;
            }
            
            // Setup button listeners
            SetupButtonListeners();
            
            // Show initial panel
            ShowPanel("MainMenu");
        }
        
        private void InitializeUI()
        {
            // Initialize UI panels dictionary
            uiPanels = new Dictionary<string, GameObject>
            {
                { "MainMenu", mainMenuPanel },
                { "Gameplay", gameplayPanel },
                { "Lobby", lobbyPanel },
                { "Pause", pausePanel },
                { "Settings", settingsPanel },
                { "Loading", loadingPanel }
            };
            
            // Hide all panels initially
            foreach (var panel in uiPanels.Values)
            {
                if (panel != null)
                {
                    panel.SetActive(false);
                }
            }
        }
        
        private void SetupButtonListeners()
        {
            // AR buttons
            if (placementButton != null)
            {
                placementButton.onClick.AddListener(() => OnPlacementButtonClicked?.Invoke());
            }
            
            if (resetTrackButton != null)
            {
                resetTrackButton.onClick.AddListener(() => OnResetTrackButtonClicked?.Invoke());
            }
            
            // Multiplayer buttons
            if (leaveRoomButton != null)
            {
                leaveRoomButton.onClick.AddListener(LeaveRoom);
            }
            
            // Settings
            if (sensitivitySlider != null)
            {
                sensitivitySlider.onValueChanged.AddListener(OnSensitivitySliderChanged);
            }
        }
        
        private void OnGameStateChanged(GameManager.GameState newState)
        {
            switch (newState)
            {
                case GameManager.GameState.MainMenu:
                    ShowPanel("MainMenu");
                    break;
                case GameManager.GameState.Lobby:
                    ShowPanel("Lobby");
                    break;
                case GameManager.GameState.Gameplay:
                    ShowPanel("Gameplay");
                    break;
                case GameManager.GameState.Paused:
                    ShowPanel("Pause");
                    break;
                case GameManager.GameState.Loading:
                    ShowPanel("Loading");
                    break;
            }
        }
        
        public void ShowPanel(string panelName)
        {
            // Hide current panel
            if (!string.IsNullOrEmpty(currentPanel) && uiPanels.ContainsKey(currentPanel))
            {
                if (uiPanels[currentPanel] != null)
                {
                    uiPanels[currentPanel].SetActive(false);
                }
            }
            
            // Show new panel
            if (uiPanels.ContainsKey(panelName) && uiPanels[panelName] != null)
            {
                uiPanels[panelName].SetActive(true);
                currentPanel = panelName;
            }
        }
        
        public void UpdateSpeedDisplay(float speed, float maxSpeed)
        {
            if (speedText != null)
            {
                speedText.text = $"{speed * 3.6f:F0} km/h"; // Convert m/s to km/h
            }
            
            if (speedometer != null)
            {
                speedometer.value = speed / maxSpeed;
            }
        }
        
        public void UpdateLapTime(float lapTime)
        {
            if (lapTimeText != null)
            {
                int minutes = Mathf.FloorToInt(lapTime / 60f);
                int seconds = Mathf.FloorToInt(lapTime % 60f);
                int milliseconds = Mathf.FloorToInt((lapTime * 1000f) % 1000f);
                
                lapTimeText.text = $"{minutes:00}:{seconds:00}.{milliseconds:000}";
            }
        }
        
        public void UpdateLapCount(int currentLap, int totalLaps)
        {
            if (lapCountText != null)
            {
                lapCountText.text = $"Lap {currentLap}/{totalLaps}";
            }
        }
        
        public void UpdatePosition(int position, int totalPlayers)
        {
            if (positionText != null)
            {
                string suffix = GetPositionSuffix(position);
                positionText.text = $"{position}{suffix} / {totalPlayers}";
            }
        }
        
        private string GetPositionSuffix(int position)
        {
            switch (position % 10)
            {
                case 1: return position % 100 == 11 ? "th" : "st";
                case 2: return position % 100 == 12 ? "th" : "nd";
                case 3: return position % 100 == 13 ? "th" : "rd";
                default: return "th";
            }
        }
        
        public void UpdateARStatus(string status)
        {
            if (arStatusText != null)
            {
                arStatusText.text = status;
            }
        }
        
        public void ShowPlacementInstructions(bool show)
        {
            if (placementInstructions != null)
            {
                placementInstructions.SetActive(show);
            }
        }
        
        public void UpdatePlayerCount(int count)
        {
            if (playerCountText != null)
            {
                playerCountText.text = $"Players: {count}";
            }
        }
        
        public void UpdateRoomName(string roomName)
        {
            if (roomNameText != null)
            {
                roomNameText.text = $"Room: {roomName}";
            }
        }
        
        public void UpdatePlayerList(List<string> playerNames)
        {
            // Clear existing items
            foreach (var item in playerListItems)
            {
                if (item != null)
                {
                    Destroy(item);
                }
            }
            playerListItems.Clear();
            
            // Create new items
            if (playerListItemPrefab != null && playerListParent != null)
            {
                foreach (string playerName in playerNames)
                {
                    GameObject item = Instantiate(playerListItemPrefab, playerListParent);
                    
                    TextMeshProUGUI nameText = item.GetComponentInChildren<TextMeshProUGUI>();
                    if (nameText != null)
                    {
                        nameText.text = playerName;
                    }
                    
                    playerListItems.Add(item);
                }
            }
        }
        
        public void SetControlsEnabled(bool enabled)
        {
            if (throttleArea != null)
            {
                throttleArea.gameObject.SetActive(enabled);
            }
            
            if (steerArea != null)
            {
                steerArea.gameObject.SetActive(enabled);
            }
            
            if (brakeButton != null)
            {
                brakeButton.gameObject.SetActive(enabled);
            }
        }
        
        public RectTransform GetThrottleArea()
        {
            return throttleArea;
        }
        
        public RectTransform GetSteerArea()
        {
            return steerArea;
        }
        
        public Button GetBrakeButton()
        {
            return brakeButton;
        }
        
        private void OnSensitivitySliderChanged(float value)
        {
            OnSensitivityChanged?.Invoke(value);
        }
        
        private void LeaveRoom()
        {
            // Handle leaving multiplayer room
            var multiplayerManager = FindObjectOfType<Multiplayer.MultiplayerManager>();
            if (multiplayerManager != null)
            {
                multiplayerManager.LeaveRoom();
            }
        }
        
        public void ShowLoadingScreen(string message = "Loading...")
        {
            ShowPanel("Loading");
            
            TextMeshProUGUI loadingText = loadingPanel?.GetComponentInChildren<TextMeshProUGUI>();
            if (loadingText != null)
            {
                loadingText.text = message;
            }
        }
        
        public void HideLoadingScreen()
        {
            if (currentPanel == "Loading")
            {
                ShowPanel("MainMenu");
            }
        }
        
        public void ShowMessage(string message, float duration = 3f)
        {
            StartCoroutine(ShowMessageCoroutine(message, duration));
        }
        
        private IEnumerator ShowMessageCoroutine(string message, float duration)
        {
            // Create temporary message UI
            GameObject messageGO = new GameObject("TempMessage");
            messageGO.transform.SetParent(transform);
            
            Canvas canvas = messageGO.AddComponent<Canvas>();
            canvas.sortingOrder = 100;
            
            TextMeshProUGUI messageText = messageGO.AddComponent<TextMeshProUGUI>();
            messageText.text = message;
            messageText.fontSize = 24;
            messageText.alignment = TextAlignmentOptions.Center;
            messageText.color = Color.white;
            
            RectTransform rectTransform = messageText.rectTransform;
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.one;
            rectTransform.offsetMin = Vector2.zero;
            rectTransform.offsetMax = Vector2.zero;
            
            yield return new WaitForSeconds(duration);
            
            Destroy(messageGO);
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            if (GameManager.Instance != null)
            {
                GameManager.Instance.OnGameStateChanged -= OnGameStateChanged;
            }
        }
    }
}

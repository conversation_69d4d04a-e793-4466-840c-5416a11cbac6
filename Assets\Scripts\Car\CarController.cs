using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace ARCarRacing.Car
{
    /// <summary>
    /// RC-style car controller with realistic physics for AR racing
    /// </summary>
    [RequireComponent(typeof(Rigidbody))]
    public class CarController : MonoBehaviour
    {
        [Header("Car Settings")]
        [SerializeField] private float maxSpeed = 10f;
        [SerializeField] private float acceleration = 5f;
        [SerializeField] private float brakeForce = 8f;
        [SerializeField] private float turnSpeed = 100f;
        [SerializeField] private float downforce = 10f;
        
        [Header("Physics")]
        [SerializeField] private float dragCoefficient = 0.98f;
        [SerializeField] private float traction = 1f;
        [SerializeField] private AnimationCurve speedCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        
        [Header("Visual")]
        [SerializeField] private Transform carModel;
        [SerializeField] private Transform[] wheels;
        [SerializeField] private float wheelRotationSpeed = 360f;
        [SerializeField] private float maxWheelTurn = 30f;
        
        [Header("Audio")]
        [SerializeField] private AudioSource engineAudioSource;
        [SerializeField] private float minPitch = 0.8f;
        [SerializeField] private float maxPitch = 2.0f;
        
        // Components
        private Rigidbody carRigidbody;
        
        // Input
        public float ThrottleInput { get; set; } = 0f;
        public float SteerInput { get; set; } = 0f;
        public bool BrakeInput { get; set; } = false;
        
        // Car state
        public float CurrentSpeed { get; private set; }
        public float SpeedKmh => CurrentSpeed * 3.6f;
        public bool IsGrounded { get; private set; }
        public Vector3 Velocity => carRigidbody.velocity;
        
        // Events
        public System.Action<float> OnSpeedChanged;
        public System.Action<bool> OnGroundedChanged;
        
        private void Awake()
        {
            carRigidbody = GetComponent<Rigidbody>();
            
            // Configure rigidbody
            carRigidbody.centerOfMass = new Vector3(0, -0.5f, 0);
            carRigidbody.drag = 0.1f;
            carRigidbody.angularDrag = 3f;
        }
        
        private void Start()
        {
            // Initialize audio
            if (engineAudioSource == null)
            {
                engineAudioSource = GetComponent<AudioSource>();
            }
        }
        
        private void Update()
        {
            // Update current speed
            CurrentSpeed = carRigidbody.velocity.magnitude;
            OnSpeedChanged?.Invoke(CurrentSpeed);
            
            // Check if grounded
            CheckGrounded();
            
            // Update visuals
            UpdateWheels();
            UpdateAudio();
        }
        
        private void FixedUpdate()
        {
            if (!IsGrounded) return;
            
            // Apply movement
            ApplyThrottle();
            ApplySteering();
            ApplyBraking();
            ApplyDownforce();
            ApplyDrag();
        }
        
        private void ApplyThrottle()
        {
            if (Mathf.Abs(ThrottleInput) < 0.1f) return;
            
            // Calculate speed factor based on current speed
            float speedFactor = speedCurve.Evaluate(CurrentSpeed / maxSpeed);
            float currentAcceleration = acceleration * speedFactor;
            
            // Apply forward/backward force
            Vector3 forceDirection = transform.forward * ThrottleInput;
            Vector3 force = forceDirection * currentAcceleration * carRigidbody.mass;
            
            // Limit max speed
            if (CurrentSpeed < maxSpeed || ThrottleInput < 0)
            {
                carRigidbody.AddForce(force, ForceMode.Force);
            }
        }
        
        private void ApplySteering()
        {
            if (Mathf.Abs(SteerInput) < 0.1f || CurrentSpeed < 0.5f) return;
            
            // Calculate turn force based on speed
            float speedFactor = Mathf.Clamp01(CurrentSpeed / 5f);
            float turnForce = SteerInput * turnSpeed * speedFactor;
            
            // Apply torque for turning
            carRigidbody.AddTorque(transform.up * turnForce * carRigidbody.mass, ForceMode.Force);
        }
        
        private void ApplyBraking()
        {
            if (!BrakeInput) return;
            
            // Apply brake force opposite to velocity
            Vector3 brakeDirection = -carRigidbody.velocity.normalized;
            Vector3 force = brakeDirection * brakeForce * carRigidbody.mass;
            
            carRigidbody.AddForce(force, ForceMode.Force);
        }
        
        private void ApplyDownforce()
        {
            // Apply downforce for better traction at high speeds
            float speedFactor = CurrentSpeed / maxSpeed;
            Vector3 downforceVector = -transform.up * downforce * speedFactor * carRigidbody.mass;
            
            carRigidbody.AddForce(downforceVector, ForceMode.Force);
        }
        
        private void ApplyDrag()
        {
            // Apply custom drag for more realistic feel
            Vector3 dragForce = -carRigidbody.velocity * dragCoefficient;
            carRigidbody.AddForce(dragForce, ForceMode.Force);
        }
        
        private void CheckGrounded()
        {
            // Raycast down to check if car is on ground
            float rayDistance = 1f;
            bool wasGrounded = IsGrounded;
            
            IsGrounded = Physics.Raycast(transform.position, -transform.up, rayDistance);
            
            if (IsGrounded != wasGrounded)
            {
                OnGroundedChanged?.Invoke(IsGrounded);
            }
        }
        
        private void UpdateWheels()
        {
            if (wheels == null || wheels.Length == 0) return;
            
            // Rotate wheels based on speed
            float wheelRotation = CurrentSpeed * wheelRotationSpeed * Time.deltaTime;
            
            for (int i = 0; i < wheels.Length; i++)
            {
                if (wheels[i] == null) continue;
                
                // Rotate wheel around X-axis for forward movement
                wheels[i].Rotate(wheelRotation, 0, 0);
                
                // Turn front wheels for steering (assuming first 2 wheels are front)
                if (i < 2)
                {
                    float wheelTurn = SteerInput * maxWheelTurn;
                    wheels[i].localRotation = Quaternion.Euler(
                        wheels[i].localRotation.eulerAngles.x,
                        wheelTurn,
                        wheels[i].localRotation.eulerAngles.z
                    );
                }
            }
        }
        
        private void UpdateAudio()
        {
            if (engineAudioSource == null) return;
            
            // Adjust engine pitch based on speed and throttle
            float speedFactor = CurrentSpeed / maxSpeed;
            float throttleFactor = Mathf.Abs(ThrottleInput);
            float targetPitch = Mathf.Lerp(minPitch, maxPitch, Mathf.Max(speedFactor, throttleFactor * 0.5f));
            
            engineAudioSource.pitch = Mathf.Lerp(engineAudioSource.pitch, targetPitch, Time.deltaTime * 5f);
            
            // Adjust volume based on throttle
            float targetVolume = Mathf.Lerp(0.3f, 1f, throttleFactor);
            engineAudioSource.volume = Mathf.Lerp(engineAudioSource.volume, targetVolume, Time.deltaTime * 3f);
        }
        
        public void SetInput(float throttle, float steer, bool brake)
        {
            ThrottleInput = Mathf.Clamp(throttle, -1f, 1f);
            SteerInput = Mathf.Clamp(steer, -1f, 1f);
            BrakeInput = brake;
        }
        
        public void ResetCar()
        {
            // Reset car position and rotation
            carRigidbody.velocity = Vector3.zero;
            carRigidbody.angularVelocity = Vector3.zero;
            
            // Reset input
            ThrottleInput = 0f;
            SteerInput = 0f;
            BrakeInput = false;
        }
        
        public void SetCarEnabled(bool enabled)
        {
            carRigidbody.isKinematic = !enabled;
            
            if (!enabled)
            {
                ResetCar();
            }
        }
        
        private void OnCollisionEnter(Collision collision)
        {
            // Handle collision effects
            if (collision.relativeVelocity.magnitude > 5f)
            {
                // Add collision effects here (particles, sound, etc.)
                Debug.Log($"Car collision with {collision.gameObject.name} at speed {collision.relativeVelocity.magnitude:F1}");
            }
        }
        
        private void OnDrawGizmosSelected()
        {
            // Draw ground check ray
            Gizmos.color = IsGrounded ? Color.green : Color.red;
            Gizmos.DrawRay(transform.position, -transform.up * 1f);
            
            // Draw velocity vector
            Gizmos.color = Color.blue;
            Gizmos.DrawRay(transform.position, carRigidbody != null ? carRigidbody.velocity : Vector3.zero);
        }
    }
}

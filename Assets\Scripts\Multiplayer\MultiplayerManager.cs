using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Photon.Pun;
using Photon.Realtime;

namespace ARCarRacing.Multiplayer
{
    /// <summary>
    /// Manages multiplayer functionality using Photon PUN2
    /// </summary>
    public class MultiplayerManager : MonoBehaviourPunPV, IConnectionCallbacks, IMatchmakingCallbacks
    {
        [Header("Connection Settings")]
        [SerializeField] private string gameVersion = "1.0";
        [SerializeField] private int maxPlayersPerRoom = 4;
        [SerializeField] private bool autoJoinLobby = true;
        
        [Header("Room Settings")]
        [SerializeField] private string defaultRoomName = "ARCarRace";
        [SerializeField] private bool isRoomVisible = true;
        [SerializeField] private bool isRoomOpen = true;
        
        // Singleton instance
        public static MultiplayerManager Instance { get; private set; }
        
        // Connection state
        public bool IsConnected => PhotonNetwork.IsConnected;
        public bool IsInRoom => PhotonNetwork.InRoom;
        public bool IsMasterClient => PhotonNetwork.IsMasterClient;
        public int PlayerCount => PhotonNetwork.CurrentRoom?.PlayerCount ?? 0;
        public string RoomName => PhotonNetwork.CurrentRoom?.Name ?? "";
        
        // Events
        public System.Action OnConnectedToMaster;
        public System.Action OnDisconnected;
        public System.Action OnJoinedRoom;
        public System.Action OnLeftRoom;
        public System.Action<Player> OnPlayerJoined;
        public System.Action<Player> OnPlayerLeft;
        public System.Action<string> OnConnectionError;
        
        // Player data
        public string PlayerName { get; private set; } = "Player";
        public int PlayerId => PhotonNetwork.LocalPlayer.ActorNumber;
        
        private void Awake()
        {
            // Singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializePhoton();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void InitializePhoton()
        {
            // Configure Photon settings
            PhotonNetwork.AutomaticallySyncScene = true;
            PhotonNetwork.GameVersion = gameVersion;
            
            // Set player name
            PlayerName = "Player_" + Random.Range(1000, 9999);
            PhotonNetwork.NickName = PlayerName;
            
            Debug.Log($"Initializing Photon with game version: {gameVersion}");
        }
        
        public void ConnectToMaster()
        {
            if (PhotonNetwork.IsConnected)
            {
                Debug.Log("Already connected to Photon");
                return;
            }
            
            Debug.Log("Connecting to Photon Master Server...");
            PhotonNetwork.ConnectUsingSettings();
        }
        
        public void Disconnect()
        {
            if (PhotonNetwork.IsConnected)
            {
                Debug.Log("Disconnecting from Photon...");
                PhotonNetwork.Disconnect();
            }
        }
        
        public void CreateRoom(string roomName = null)
        {
            if (!PhotonNetwork.IsConnectedAndReady)
            {
                Debug.LogWarning("Not connected to Photon Master Server");
                return;
            }
            
            if (string.IsNullOrEmpty(roomName))
            {
                roomName = defaultRoomName + "_" + Random.Range(1000, 9999);
            }
            
            RoomOptions roomOptions = new RoomOptions
            {
                MaxPlayers = maxPlayersPerRoom,
                IsVisible = isRoomVisible,
                IsOpen = isRoomOpen
            };
            
            Debug.Log($"Creating room: {roomName}");
            PhotonNetwork.CreateRoom(roomName, roomOptions);
        }
        
        public void JoinRoom(string roomName)
        {
            if (!PhotonNetwork.IsConnectedAndReady)
            {
                Debug.LogWarning("Not connected to Photon Master Server");
                return;
            }
            
            Debug.Log($"Joining room: {roomName}");
            PhotonNetwork.JoinRoom(roomName);
        }
        
        public void JoinRandomRoom()
        {
            if (!PhotonNetwork.IsConnectedAndReady)
            {
                Debug.LogWarning("Not connected to Photon Master Server");
                return;
            }
            
            Debug.Log("Joining random room...");
            PhotonNetwork.JoinRandomRoom();
        }
        
        public void LeaveRoom()
        {
            if (PhotonNetwork.InRoom)
            {
                Debug.Log("Leaving room...");
                PhotonNetwork.LeaveRoom();
            }
        }
        
        public void SetPlayerName(string name)
        {
            if (string.IsNullOrEmpty(name)) return;
            
            PlayerName = name;
            PhotonNetwork.NickName = name;
            
            Debug.Log($"Player name set to: {name}");
        }
        
        public List<RoomInfo> GetRoomList()
        {
            List<RoomInfo> roomList = new List<RoomInfo>();
            
            if (PhotonNetwork.IsConnectedAndReady)
            {
                // This would be populated by OnRoomListUpdate callback
                // For now, return empty list
            }
            
            return roomList;
        }
        
        public Player[] GetPlayersInRoom()
        {
            if (PhotonNetwork.CurrentRoom != null)
            {
                return PhotonNetwork.CurrentRoom.Players.Values.ToArray();
            }
            
            return new Player[0];
        }
        
        public void SendCustomEvent(string eventName, object data)
        {
            if (!PhotonNetwork.InRoom) return;
            
            ExitGames.Client.Photon.Hashtable eventData = new ExitGames.Client.Photon.Hashtable();
            eventData["eventName"] = eventName;
            eventData["data"] = data;
            
            PhotonNetwork.RaiseEvent(1, eventData, RaiseEventOptions.Default, SendOptions.SendReliable);
        }
        
        // IConnectionCallbacks implementation
        public void OnConnectedToMaster()
        {
            Debug.Log("Connected to Photon Master Server");
            
            if (autoJoinLobby)
            {
                PhotonNetwork.JoinLobby();
            }
            
            OnConnectedToMaster?.Invoke();
        }
        
        public void OnDisconnected(DisconnectCause cause)
        {
            Debug.Log($"Disconnected from Photon: {cause}");
            OnDisconnected?.Invoke();
        }
        
        public void OnRegionListReceived(RegionHandler regionHandler)
        {
            Debug.Log("Region list received");
        }
        
        public void OnCustomAuthenticationResponse(Dictionary<string, object> data)
        {
            Debug.Log("Custom authentication response received");
        }
        
        public void OnCustomAuthenticationFailed(string debugMessage)
        {
            Debug.LogError($"Custom authentication failed: {debugMessage}");
            OnConnectionError?.Invoke(debugMessage);
        }
        
        // IMatchmakingCallbacks implementation
        public void OnFriendListUpdate(List<FriendInfo> friendList)
        {
            Debug.Log("Friend list updated");
        }
        
        public void OnCreatedRoom()
        {
            Debug.Log($"Room created successfully: {PhotonNetwork.CurrentRoom.Name}");
        }
        
        public void OnCreateRoomFailed(short returnCode, string message)
        {
            Debug.LogError($"Failed to create room: {message}");
            OnConnectionError?.Invoke($"Failed to create room: {message}");
        }
        
        public void OnJoinedRoom()
        {
            Debug.Log($"Joined room: {PhotonNetwork.CurrentRoom.Name} with {PhotonNetwork.CurrentRoom.PlayerCount} players");
            OnJoinedRoom?.Invoke();
        }
        
        public void OnJoinRoomFailed(short returnCode, string message)
        {
            Debug.LogError($"Failed to join room: {message}");
            OnConnectionError?.Invoke($"Failed to join room: {message}");
        }
        
        public void OnJoinRandomFailed(short returnCode, string message)
        {
            Debug.LogWarning($"Failed to join random room: {message}. Creating new room...");
            CreateRoom();
        }
        
        public void OnLeftRoom()
        {
            Debug.Log("Left room");
            OnLeftRoom?.Invoke();
        }
        
        public void OnRoomListUpdate(List<RoomInfo> roomList)
        {
            Debug.Log($"Room list updated: {roomList.Count} rooms available");
        }
        
        public void OnLobbyStatisticsUpdate(List<TypedLobbyInfo> lobbyStatistics)
        {
            Debug.Log("Lobby statistics updated");
        }
        
        // PUN Callbacks
        public override void OnPlayerEnteredRoom(Player newPlayer)
        {
            Debug.Log($"Player joined: {newPlayer.NickName}");
            OnPlayerJoined?.Invoke(newPlayer);
        }
        
        public override void OnPlayerLeftRoom(Player otherPlayer)
        {
            Debug.Log($"Player left: {otherPlayer.NickName}");
            OnPlayerLeft?.Invoke(otherPlayer);
        }
        
        public override void OnMasterClientSwitched(Player newMasterClient)
        {
            Debug.Log($"Master client switched to: {newMasterClient.NickName}");
        }
        
        private void OnApplicationPause(bool pauseStatus)
        {
            // Handle app pause/resume for mobile
            if (pauseStatus && PhotonNetwork.IsConnected)
            {
                // Optionally disconnect when app is paused
                // PhotonNetwork.Disconnect();
            }
        }
        
        private void OnApplicationFocus(bool hasFocus)
        {
            // Handle app focus for mobile
            if (!hasFocus && PhotonNetwork.IsConnected)
            {
                // Handle loss of focus
            }
        }
    }
}

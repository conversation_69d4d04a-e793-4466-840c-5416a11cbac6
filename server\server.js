const WebSocket = require('ws');
const express = require('express');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');
const path = require('path');

// Configuration
const PORT = process.env.PORT || 8080;
const WS_PORT = process.env.WS_PORT || 8081;

// Express app for serving static files
const app = express();
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../Build')));

// Serve the Unity WebGL build
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../Build/index.html'));
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        timestamp: new Date().toISOString(),
        rooms: Object.keys(rooms).length,
        players: Object.keys(players).length
    });
});

// Start HTTP server
app.listen(PORT, () => {
    console.log(`🌐 HTTP Server running on port ${PORT}`);
    console.log(`📁 Serving Unity build from: ${path.join(__dirname, '../Build')}`);
});

// WebSocket server for multiplayer
const wss = new WebSocket.Server({ port: WS_PORT });

// Game state
const rooms = new Map();
const players = new Map();

// Room class
class GameRoom {
    constructor(id, name, maxPlayers = 4) {
        this.id = id;
        this.name = name;
        this.maxPlayers = maxPlayers;
        this.players = new Map();
        this.gameState = 'waiting'; // waiting, playing, finished
        this.createdAt = new Date();
        this.trackData = null;
    }
    
    addPlayer(playerId, playerData) {
        if (this.players.size >= this.maxPlayers) {
            return false;
        }
        
        this.players.set(playerId, {
            ...playerData,
            joinedAt: new Date(),
            isReady: false
        });
        
        return true;
    }
    
    removePlayer(playerId) {
        return this.players.delete(playerId);
    }
    
    getPlayerCount() {
        return this.players.size;
    }
    
    getAllPlayers() {
        return Array.from(this.players.values());
    }
    
    updatePlayerData(playerId, data) {
        if (this.players.has(playerId)) {
            const player = this.players.get(playerId);
            this.players.set(playerId, { ...player, ...data });
            return true;
        }
        return false;
    }
    
    setTrackData(trackData) {
        this.trackData = trackData;
    }
    
    canStartGame() {
        return this.players.size >= 1 && 
               Array.from(this.players.values()).every(p => p.isReady);
    }
}

// Player class
class Player {
    constructor(ws, id) {
        this.ws = ws;
        this.id = id;
        this.name = `Player_${id.substring(0, 8)}`;
        this.roomId = null;
        this.isAlive = true;
        this.lastPing = Date.now();
    }
    
    send(message) {
        if (this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        }
    }
    
    joinRoom(roomId) {
        this.roomId = roomId;
    }
    
    leaveRoom() {
        this.roomId = null;
    }
}

// WebSocket connection handler
wss.on('connection', (ws) => {
    const playerId = uuidv4();
    const player = new Player(ws, playerId);
    players.set(playerId, player);
    
    console.log(`🎮 Player connected: ${playerId}`);
    
    // Send welcome message
    player.send({
        type: 'connected',
        playerId: playerId,
        message: 'Welcome to AR Car Racing!'
    });
    
    // Message handler
    ws.on('message', (data) => {
        try {
            const message = JSON.parse(data);
            handleMessage(player, message);
        } catch (error) {
            console.error('Error parsing message:', error);
            player.send({
                type: 'error',
                error: 'Invalid message format'
            });
        }
    });
    
    // Connection close handler
    ws.on('close', () => {
        console.log(`👋 Player disconnected: ${playerId}`);
        handlePlayerDisconnect(player);
    });
    
    // Error handler
    ws.on('error', (error) => {
        console.error(`❌ WebSocket error for player ${playerId}:`, error);
    });
    
    // Ping/pong for connection health
    ws.on('pong', () => {
        player.lastPing = Date.now();
        player.isAlive = true;
    });
});

// Message handling
function handleMessage(player, message) {
    switch (message.type) {
        case 'create_room':
            handleCreateRoom(player, message);
            break;
            
        case 'join_room':
            handleJoinRoom(player, message);
            break;
            
        case 'leave_room':
            handleLeaveRoom(player, message);
            break;
            
        case 'player_data':
            handlePlayerData(player, message);
            break;
            
        case 'chat_message':
            handleChatMessage(player, message);
            break;
            
        case 'track_data':
            handleTrackData(player, message);
            break;
            
        case 'game_event':
            handleGameEvent(player, message);
            break;
            
        case 'ping':
            player.send({ type: 'pong', timestamp: message.timestamp });
            break;
            
        default:
            console.log(`Unknown message type: ${message.type}`);
            player.send({
                type: 'error',
                error: `Unknown message type: ${message.type}`
            });
    }
}

// Create room handler
function handleCreateRoom(player, message) {
    const roomId = uuidv4();
    const roomName = message.roomName || `Room_${roomId.substring(0, 8)}`;
    
    const room = new GameRoom(roomId, roomName);
    rooms.set(roomId, room);
    
    // Add player to room
    const playerData = {
        id: player.id,
        name: player.name,
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0, z: 0, w: 1 }
    };
    
    room.addPlayer(player.id, playerData);
    player.joinRoom(roomId);
    
    console.log(`🏠 Room created: ${roomId} by ${player.id}`);
    
    player.send({
        type: 'room_created',
        roomId: roomId,
        roomName: roomName
    });
    
    // Broadcast room list update
    broadcastRoomList();
}

// Join room handler
function handleJoinRoom(player, message) {
    const roomId = message.roomId;
    const room = rooms.get(roomId);
    
    if (!room) {
        player.send({
            type: 'error',
            error: 'Room not found'
        });
        return;
    }
    
    const playerData = {
        id: player.id,
        name: player.name,
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0, z: 0, w: 1 }
    };
    
    if (room.addPlayer(player.id, playerData)) {
        player.joinRoom(roomId);
        
        console.log(`🚪 Player ${player.id} joined room ${roomId}`);
        
        player.send({
            type: 'room_joined',
            roomId: roomId,
            roomName: room.name,
            players: room.getAllPlayers()
        });
        
        // Notify other players
        broadcastToRoom(roomId, {
            type: 'player_joined',
            playerData: playerData
        }, player.id);
        
        // Send track data if available
        if (room.trackData) {
            player.send({
                type: 'track_data',
                data: room.trackData
            });
        }
    } else {
        player.send({
            type: 'error',
            error: 'Room is full'
        });
    }
}

// Leave room handler
function handleLeaveRoom(player, message) {
    if (!player.roomId) return;
    
    const room = rooms.get(player.roomId);
    if (room) {
        room.removePlayer(player.id);
        
        console.log(`🚪 Player ${player.id} left room ${player.roomId}`);
        
        // Notify other players
        broadcastToRoom(player.roomId, {
            type: 'player_left',
            playerId: player.id
        }, player.id);
        
        // Remove empty rooms
        if (room.getPlayerCount() === 0) {
            rooms.delete(player.roomId);
            console.log(`🗑️ Empty room removed: ${player.roomId}`);
        }
    }
    
    player.leaveRoom();
    broadcastRoomList();
}

// Player data handler
function handlePlayerData(player, message) {
    if (!player.roomId) return;
    
    const room = rooms.get(player.roomId);
    if (room && room.updatePlayerData(player.id, message.data)) {
        // Broadcast to other players in the room
        broadcastToRoom(player.roomId, {
            type: 'player_data',
            playerId: player.id,
            data: message.data
        }, player.id);
    }
}

// Chat message handler
function handleChatMessage(player, message) {
    if (!player.roomId) return;
    
    console.log(`💬 Chat from ${player.id}: ${message.message}`);
    
    // Broadcast to all players in the room
    broadcastToRoom(player.roomId, {
        type: 'chat_message',
        playerId: player.id,
        playerName: player.name,
        message: message.message,
        timestamp: Date.now()
    });
}

// Track data handler
function handleTrackData(player, message) {
    if (!player.roomId) return;
    
    const room = rooms.get(player.roomId);
    if (room) {
        room.setTrackData(message.data);
        
        console.log(`🛤️ Track data updated in room ${player.roomId}`);
        
        // Broadcast to other players
        broadcastToRoom(player.roomId, {
            type: 'track_data',
            data: message.data
        }, player.id);
    }
}

// Game event handler
function handleGameEvent(player, message) {
    if (!player.roomId) return;
    
    console.log(`🎮 Game event from ${player.id}: ${message.event}`);
    
    // Broadcast to all players in the room
    broadcastToRoom(player.roomId, {
        type: 'game_event',
        playerId: player.id,
        event: message.event,
        data: message.data,
        timestamp: Date.now()
    });
}

// Player disconnect handler
function handlePlayerDisconnect(player) {
    if (player.roomId) {
        handleLeaveRoom(player, {});
    }
    
    players.delete(player.id);
}

// Broadcast message to all players in a room
function broadcastToRoom(roomId, message, excludePlayerId = null) {
    const room = rooms.get(roomId);
    if (!room) return;
    
    room.players.forEach((playerData, playerId) => {
        if (playerId !== excludePlayerId) {
            const player = players.get(playerId);
            if (player) {
                player.send(message);
            }
        }
    });
}

// Broadcast room list to all players
function broadcastRoomList() {
    const roomList = Array.from(rooms.values()).map(room => ({
        id: room.id,
        name: room.name,
        playerCount: room.getPlayerCount(),
        maxPlayers: room.maxPlayers,
        gameState: room.gameState
    }));
    
    const message = {
        type: 'room_list',
        rooms: roomList
    };
    
    players.forEach(player => {
        if (!player.roomId) { // Only send to players not in a room
            player.send(message);
        }
    });
}

// Ping all connections periodically
setInterval(() => {
    wss.clients.forEach((ws) => {
        if (ws.isAlive === false) {
            return ws.terminate();
        }
        
        ws.isAlive = false;
        ws.ping();
    });
}, 30000); // 30 seconds

// Clean up inactive rooms
setInterval(() => {
    const now = Date.now();
    const ROOM_TIMEOUT = 30 * 60 * 1000; // 30 minutes
    
    rooms.forEach((room, roomId) => {
        if (room.getPlayerCount() === 0 && (now - room.createdAt.getTime()) > ROOM_TIMEOUT) {
            rooms.delete(roomId);
            console.log(`🧹 Cleaned up inactive room: ${roomId}`);
        }
    });
}, 5 * 60 * 1000); // Check every 5 minutes

console.log(`🚀 WebSocket server running on port ${WS_PORT}`);
console.log(`🎮 AR Car Racing multiplayer server ready!`);

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    
    // Close all WebSocket connections
    wss.clients.forEach((ws) => {
        ws.close();
    });
    
    wss.close(() => {
        console.log('✅ WebSocket server closed');
        process.exit(0);
    });
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
    process.exit(0);
});

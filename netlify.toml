[build]
  publish = "."
  command = "echo 'No build step required - serving static files'"

[build.environment]
  NODE_VERSION = "18"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=*, microphone=*, geolocation=*, gyroscope=*, accelerometer=*, magnetometer=*"

[[headers]]
  for = "/demo/*"
  [headers.values]
    Cache-Control = "public, max-age=3600"

[[headers]]
  for = "*.html"
  [headers.values]
    Cache-Control = "public, max-age=300"

[[headers]]
  for = "*.js"
  [headers.values]
    Content-Type = "application/javascript"
    Cache-Control = "public, max-age=86400"

[[headers]]
  for = "*.css"
  [headers.values]
    Content-Type = "text/css"
    Cache-Control = "public, max-age=86400"

[[headers]]
  for = "*.wasm"
  [headers.values]
    Content-Type = "application/wasm"
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "*.data"
  [headers.values]
    Content-Type = "application/octet-stream"
    Cache-Control = "public, max-age=31536000"

[[redirects]]
  from = "/"
  to = "/demo/index.html"
  status = 200

[[redirects]]
  from = "/demo"
  to = "/demo/index.html"
  status = 200

# Enable HTTPS redirect
[[redirects]]
  from = "http://ar-car-racing.netlify.app/*"
  to = "https://ar-car-racing.netlify.app/:splat"
  status = 301
  force = true

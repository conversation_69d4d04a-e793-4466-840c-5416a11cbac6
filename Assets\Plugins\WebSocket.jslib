// WebSocket plugin for Unity WebGL multiplayer
var WebSocketPlugin = {
    // WebSocket connection
    socket: null,
    isConnected: false,
    currentRoom: null,
    playerId: null,
    
    // Unity callback object name
    unityObjectName: "WebMultiplayerManager",
    
    // Connect to WebSocket server
    ConnectToServer: function(urlPtr) {
        var url = UTF8ToString(urlPtr);
        
        try {
            WebSocketPlugin.socket = new WebSocket(url);
            
            WebSocketPlugin.socket.onopen = function(event) {
                console.log('Connected to WebSocket server');
                WebSocketPlugin.isConnected = true;
                
                // Generate player ID
                WebSocketPlugin.playerId = WebSocketPlugin.generateId();
                
                // Notify Unity
                if (typeof unityInstance !== 'undefined') {
                    unityInstance.SendMessage(WebSocketPlugin.unityObjectName, 'OnServerConnected', '');
                }
            };
            
            WebSocketPlugin.socket.onmessage = function(event) {
                WebSocketPlugin.handleMessage(event.data);
            };
            
            WebSocketPlugin.socket.onclose = function(event) {
                console.log('WebSocket connection closed');
                WebSocketPlugin.isConnected = false;
                WebSocketPlugin.currentRoom = null;
                
                // Notify Unity
                if (typeof unityInstance !== 'undefined') {
                    unityInstance.SendMessage(WebSocketPlugin.unityObjectName, 'OnServerDisconnected', '');
                }
            };
            
            WebSocketPlugin.socket.onerror = function(error) {
                console.error('WebSocket error:', error);
                
                // Notify Unity
                if (typeof unityInstance !== 'undefined') {
                    unityInstance.SendMessage(WebSocketPlugin.unityObjectName, 'OnMultiplayerError', error.toString());
                }
            };
            
        } catch (error) {
            console.error('Failed to connect to WebSocket:', error);
            
            // Notify Unity
            if (typeof unityInstance !== 'undefined') {
                unityInstance.SendMessage(WebSocketPlugin.unityObjectName, 'OnMultiplayerError', error.toString());
            }
        }
    },
    
    // Disconnect from server
    DisconnectFromServer: function() {
        if (WebSocketPlugin.socket && WebSocketPlugin.isConnected) {
            WebSocketPlugin.socket.close();
            WebSocketPlugin.socket = null;
            WebSocketPlugin.isConnected = false;
            WebSocketPlugin.currentRoom = null;
        }
    },
    
    // Create a new room
    CreateRoom: function(roomNamePtr) {
        if (!WebSocketPlugin.isConnected) return;
        
        var roomName = UTF8ToString(roomNamePtr);
        var message = {
            type: 'create_room',
            roomName: roomName,
            playerId: WebSocketPlugin.playerId,
            timestamp: Date.now()
        };
        
        WebSocketPlugin.sendMessage(message);
    },
    
    // Join an existing room
    JoinRoom: function(roomIdPtr) {
        if (!WebSocketPlugin.isConnected) return;
        
        var roomId = UTF8ToString(roomIdPtr);
        var message = {
            type: 'join_room',
            roomId: roomId,
            playerId: WebSocketPlugin.playerId,
            timestamp: Date.now()
        };
        
        WebSocketPlugin.sendMessage(message);
    },
    
    // Leave current room
    LeaveRoom: function() {
        if (!WebSocketPlugin.isConnected || !WebSocketPlugin.currentRoom) return;
        
        var message = {
            type: 'leave_room',
            roomId: WebSocketPlugin.currentRoom,
            playerId: WebSocketPlugin.playerId,
            timestamp: Date.now()
        };
        
        WebSocketPlugin.sendMessage(message);
        WebSocketPlugin.currentRoom = null;
    },
    
    // Send player data
    SendPlayerData: function(dataPtr) {
        if (!WebSocketPlugin.isConnected || !WebSocketPlugin.currentRoom) return;
        
        var data = UTF8ToString(dataPtr);
        var playerData = JSON.parse(data);
        
        var message = {
            type: 'player_data',
            roomId: WebSocketPlugin.currentRoom,
            playerId: WebSocketPlugin.playerId,
            data: playerData,
            timestamp: Date.now()
        };
        
        WebSocketPlugin.sendMessage(message);
    },
    
    // Send chat message
    SendChatMessage: function(messagePtr) {
        if (!WebSocketPlugin.isConnected || !WebSocketPlugin.currentRoom) return;
        
        var messageText = UTF8ToString(messagePtr);
        var message = {
            type: 'chat_message',
            roomId: WebSocketPlugin.currentRoom,
            playerId: WebSocketPlugin.playerId,
            message: messageText,
            timestamp: Date.now()
        };
        
        WebSocketPlugin.sendMessage(message);
    },
    
    // Handle incoming messages
    handleMessage: function(data) {
        try {
            var message = JSON.parse(data);
            
            switch (message.type) {
                case 'room_created':
                    WebSocketPlugin.currentRoom = message.roomId;
                    if (typeof unityInstance !== 'undefined') {
                        unityInstance.SendMessage(WebSocketPlugin.unityObjectName, 'OnRoomCreated', message.roomId);
                    }
                    break;
                    
                case 'room_joined':
                    WebSocketPlugin.currentRoom = message.roomId;
                    if (typeof unityInstance !== 'undefined') {
                        unityInstance.SendMessage(WebSocketPlugin.unityObjectName, 'OnRoomJoined', message.roomId);
                    }
                    break;
                    
                case 'player_joined':
                    if (typeof unityInstance !== 'undefined') {
                        unityInstance.SendMessage(WebSocketPlugin.unityObjectName, 'OnPlayerDataReceived', JSON.stringify(message.playerData));
                    }
                    break;
                    
                case 'player_left':
                    if (typeof unityInstance !== 'undefined') {
                        unityInstance.SendMessage(WebSocketPlugin.unityObjectName, 'OnPlayerDisconnected', message.playerId);
                    }
                    break;
                    
                case 'player_data':
                    if (message.playerId !== WebSocketPlugin.playerId && typeof unityInstance !== 'undefined') {
                        unityInstance.SendMessage(WebSocketPlugin.unityObjectName, 'OnPlayerDataReceived', JSON.stringify(message.data));
                    }
                    break;
                    
                case 'chat_message':
                    if (typeof unityInstance !== 'undefined') {
                        var chatData = {
                            playerId: message.playerId,
                            message: message.message,
                            timestamp: message.timestamp
                        };
                        unityInstance.SendMessage(WebSocketPlugin.unityObjectName, 'OnChatMessageReceived', JSON.stringify(chatData));
                    }
                    break;
                    
                case 'error':
                    console.error('Server error:', message.error);
                    if (typeof unityInstance !== 'undefined') {
                        unityInstance.SendMessage(WebSocketPlugin.unityObjectName, 'OnMultiplayerError', message.error);
                    }
                    break;
                    
                default:
                    console.log('Unknown message type:', message.type);
                    break;
            }
        } catch (error) {
            console.error('Error parsing message:', error);
        }
    },
    
    // Send message to server
    sendMessage: function(message) {
        if (WebSocketPlugin.socket && WebSocketPlugin.isConnected) {
            try {
                WebSocketPlugin.socket.send(JSON.stringify(message));
            } catch (error) {
                console.error('Error sending message:', error);
            }
        }
    },
    
    // Generate unique ID
    generateId: function() {
        return 'player_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    },
    
    // Get connection status
    IsConnected: function() {
        return WebSocketPlugin.isConnected;
    },
    
    // Get current room
    GetCurrentRoom: function() {
        return WebSocketPlugin.currentRoom;
    },
    
    // Get player ID
    GetPlayerId: function() {
        return WebSocketPlugin.playerId;
    }
};

// Simple WebRTC peer-to-peer connection (alternative to WebSocket)
var WebRTCPlugin = {
    // WebRTC connections
    localConnection: null,
    remoteConnection: null,
    dataChannel: null,
    isConnected: false,
    
    // Initialize WebRTC
    InitializeWebRTC: function() {
        try {
            // Create peer connection
            WebRTCPlugin.localConnection = new RTCPeerConnection({
                iceServers: [
                    { urls: 'stun:stun.l.google.com:19302' },
                    { urls: 'stun:stun1.l.google.com:19302' }
                ]
            });
            
            // Create data channel
            WebRTCPlugin.dataChannel = WebRTCPlugin.localConnection.createDataChannel('gameData', {
                ordered: true
            });
            
            WebRTCPlugin.dataChannel.onopen = function() {
                console.log('WebRTC data channel opened');
                WebRTCPlugin.isConnected = true;
            };
            
            WebRTCPlugin.dataChannel.onmessage = function(event) {
                WebRTCPlugin.handleDataChannelMessage(event.data);
            };
            
            WebRTCPlugin.dataChannel.onclose = function() {
                console.log('WebRTC data channel closed');
                WebRTCPlugin.isConnected = false;
            };
            
            return true;
        } catch (error) {
            console.error('Failed to initialize WebRTC:', error);
            return false;
        }
    },
    
    // Handle data channel messages
    handleDataChannelMessage: function(data) {
        try {
            var message = JSON.parse(data);
            
            // Forward to Unity
            if (typeof unityInstance !== 'undefined') {
                switch (message.type) {
                    case 'player_data':
                        unityInstance.SendMessage('WebMultiplayerManager', 'OnPlayerDataReceived', JSON.stringify(message.data));
                        break;
                    default:
                        console.log('Unknown WebRTC message:', message.type);
                        break;
                }
            }
        } catch (error) {
            console.error('Error parsing WebRTC message:', error);
        }
    },
    
    // Send data via WebRTC
    SendWebRTCData: function(dataPtr) {
        if (!WebRTCPlugin.isConnected || !WebRTCPlugin.dataChannel) return;
        
        var data = UTF8ToString(dataPtr);
        
        try {
            WebRTCPlugin.dataChannel.send(data);
        } catch (error) {
            console.error('Error sending WebRTC data:', error);
        }
    }
};

// Export functions for Unity
mergeInto(LibraryManager.library, WebSocketPlugin);
mergeInto(LibraryManager.library, WebRTCPlugin);

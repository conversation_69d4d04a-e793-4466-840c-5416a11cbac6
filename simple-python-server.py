#!/usr/bin/env python3
"""
AR Car Racing - Simple Python Web Server
Serves the demo files with proper MIME types for WebXR support
"""

import http.server
import socketserver
import os
import sys
import webbrowser
from urllib.parse import urlparse
import mimetypes

# Configuration
PORT = 3000
HOST = '0.0.0.0'

# Add custom MIME types for Unity WebGL
mimetypes.add_type('application/wasm', '.wasm')
mimetypes.add_type('application/octet-stream', '.data')
mimetypes.add_type('application/javascript', '.framework')
mimetypes.add_type('application/octet-stream', '.symbols')

class ARCarRacingHandler(http.server.SimpleHTTPRequestHandler):
    """Custom handler with CORS and proper headers for WebXR"""
    
    def end_headers(self):
        # Add CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        
        # Add security headers
        self.send_header('X-Frame-Options', 'DENY')
        self.send_header('X-Content-Type-Options', 'nosniff')
        
        # Add permissions for WebXR
        self.send_header('Permissions-Policy', 'camera=*, microphone=*, geolocation=*, gyroscope=*, accelerometer=*, magnetometer=*')
        
        super().end_headers()
    
    def do_GET(self):
        """Handle GET requests with custom routing"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # Route root to demo
        if path == '/':
            path = '/demo/index.html'
        elif path == '/demo':
            path = '/demo/index.html'
        elif path == '/health':
            self.send_health_check()
            return
        
        # Update the path for the parent handler
        self.path = path
        
        # Log the request
        print(f"📡 {self.command} {self.path}")
        
        # Call parent handler
        super().do_GET()
    
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.end_headers()
    
    def send_health_check(self):
        """Send health check response"""
        import json
        import time
        
        health_data = {
            'status': 'ok',
            'timestamp': time.time(),
            'server': 'AR Car Racing Python Server',
            'version': '1.0.0'
        }
        
        response = json.dumps(health_data, indent=2)
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        self.wfile.write(response.encode())
    
    def log_message(self, format, *args):
        """Custom log format"""
        print(f"🌐 {args[0]} - {args[1]} - {args[2]}")

def get_local_ip():
    """Get local IP address"""
    import socket
    try:
        # Connect to a remote server to get local IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except:
        return "localhost"

def main():
    """Start the server"""
    print("🏎️ AR Car Racing - Python Web Server")
    print("====================================")
    
    # Check if demo directory exists
    if not os.path.exists('demo'):
        print("❌ Demo directory not found!")
        print("   Make sure you're running this from the project root directory.")
        sys.exit(1)
    
    # Change to project root directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # Create server
        with socketserver.TCPServer((HOST, PORT), ARCarRacingHandler) as httpd:
            local_ip = get_local_ip()
            
            print(f"🚀 Server started successfully!")
            print(f"📡 Local access: http://localhost:{PORT}")
            print(f"🌐 Network access: http://{local_ip}:{PORT}")
            print(f"📱 Mobile access: http://{local_ip}:{PORT}")
            print("")
            print("🎮 Available endpoints:")
            print(f"   • Demo: http://localhost:{PORT}/")
            print(f"   • Health: http://localhost:{PORT}/health")
            print("")
            print("📱 For AR features, access from mobile device")
            print("🔒 HTTPS required for full WebXR support")
            print("🛑 Press Ctrl+C to stop the server")
            print("")
            
            # Try to open browser
            try:
                webbrowser.open(f'http://localhost:{PORT}')
                print("🌐 Opening browser...")
            except:
                print("💡 Manually open: http://localhost:{PORT}")
            
            print("✅ Server is running...")
            
            # Start serving
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 48 or e.errno == 10048:  # Address already in use
            print(f"❌ Port {PORT} is already in use!")
            print("   Try a different port:")
            print(f"   python simple-python-server.py {PORT + 1}")
        else:
            print(f"❌ Server error: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        print("✅ Goodbye!")
        sys.exit(0)

if __name__ == "__main__":
    # Allow custom port from command line
    if len(sys.argv) > 1:
        try:
            PORT = int(sys.argv[1])
        except ValueError:
            print("❌ Invalid port number")
            sys.exit(1)
    
    main()

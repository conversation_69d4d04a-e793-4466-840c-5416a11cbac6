@echo off
echo 🚀 AR Car Racing - Web Deployment Script
echo ========================================

echo Checking prerequisites...

:: Check if Python is available
python --version >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Python not found. Please install Python from https://python.org/
    echo    Alternative: Use the Node.js server if you have Node.js installed
    pause
    exit /b 1
)

echo ✅ Python found!

:: Check if demo directory exists
if not exist "demo" (
    echo ❌ Demo directory not found!
    echo    Make sure you're in the project root directory
    pause
    exit /b 1
)

echo ✅ Demo files found!

echo.
echo 🌐 Starting local web server...
echo ===============================
echo.
echo 📡 Server will be available at:
echo    • Local: http://localhost:3000
echo    • Network: http://[your-ip]:3000
echo.
echo 📱 For mobile AR testing:
echo    1. Connect mobile device to same WiFi
echo    2. Open Chrome/Edge on mobile
echo    3. Visit http://[your-ip]:3000
echo    4. Allow camera permissions
echo.
echo 🔒 Note: Full AR features require HTTPS
echo    For production, deploy to Netlify/Vercel
echo.
echo 🛑 Press Ctrl+C to stop the server
echo.

:: Start the Python server
python simple-python-server.py

echo.
echo 👋 Server stopped. Thanks for using AR Car Racing!
pause

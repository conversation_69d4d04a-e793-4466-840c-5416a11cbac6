# AR Car Racing Game

A multiplayer augmented reality car racing game for mobile devices (Android & iOS).

## 🎮 Game Features

- **AR Track Placement**: Use ARCore/ARKit to place virtual racetracks on real-world surfaces
- **Multiplayer Racing**: Local multiplayer via Wi-Fi/Bluetooth with shared AR space
- **Multiple Game Modes**: Time Trial, Battle Mode, Checkpoint Race, Item Pickup, Free Drive
- **Car Customization**: Customize colors, decals, and unlock new vehicles
- **Touch Controls**: Simple controls for acceleration, braking, and steering

## 🛠️ Tech Stack

- **Unity 2022.3 LTS** - Game engine
- **AR Foundation 5.0+** - Cross-platform AR framework
- **ARCore** (Android) / **ARKit** (iOS) - Platform-specific AR
- **Photon PUN2** - Multiplayer networking
- **C#** - Programming language

## 📱 Target Platforms

- Android 7.0+ (API level 24+) with ARCore support
- iOS 11.0+ with ARKit support

## 🏗️ Project Structure

```
Assets/
├── Scripts/
│   ├── AR/              # AR-related scripts
│   ├── Car/             # Car controller and physics
│   ├── Multiplayer/     # Networking and synchronization
│   ├── GameModes/       # Different game mode implementations
│   ├── UI/              # User interface scripts
│   └── Managers/        # Game managers and singletons
├── Prefabs/
│   ├── Cars/            # Car prefabs
│   ├── Track/           # Track pieces and checkpoints
│   ├── UI/              # UI prefabs
│   └── Effects/         # Particle effects and audio
├── Materials/           # Materials and shaders
├── Textures/            # Textures and sprites
├── Audio/               # Sound effects and music
├── Scenes/              # Game scenes
└── StreamingAssets/     # Platform-specific assets
```

## 🚀 Getting Started

1. **Prerequisites**:
   - Unity 2022.3 LTS or newer
   - Android SDK (for Android builds)
   - Xcode (for iOS builds)
   - ARCore/ARKit compatible device for testing

2. **Setup**:
   - Clone this repository
   - Open the project in Unity
   - Install required packages via Package Manager
   - Configure build settings for your target platform

3. **Building**:
   - Switch to Android/iOS platform in Build Settings
   - Configure player settings (permissions, orientation, etc.)
   - Build and deploy to device

## 📋 Development Roadmap

- [x] Project Setup and Architecture
- [ ] AR Foundation Integration
- [ ] Core Car Controller System
- [ ] Track System Development
- [ ] Multiplayer Networking Foundation
- [ ] Shared AR Space Synchronization
- [ ] Game Modes Implementation
- [ ] UI and Game Flow
- [ ] Car Customization System
- [ ] Audio and Visual Polish
- [ ] Testing and Platform Optimization
- [ ] Build and Deployment

## 🎯 Game Modes

### Time Trial
Race against the clock to achieve the best lap time.

### Battle Mode
Bump into opponents to push them off the track while avoiding being pushed yourself.

### Checkpoint Race
Navigate through AR checkpoints in the correct order to complete the race.

### Item Pickup Mode
Collect power-ups and items scattered around the track for advantages.

### Free Drive
Explore and drive without rules or objectives.

## 🔧 Configuration

The game supports various configuration options through the Settings Manager:
- Graphics quality settings
- Audio volume controls
- Control sensitivity
- Multiplayer preferences

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

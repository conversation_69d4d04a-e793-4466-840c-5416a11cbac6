# AR Car Racing Game

A multiplayer augmented reality car racing game for mobile devices (Android & iOS).

## 🎮 Game Features

- **AR Track Placement**: Use ARCore/ARKit to place virtual racetracks on real-world surfaces
- **Multiplayer Racing**: Local multiplayer via Wi-Fi/Bluetooth with shared AR space
- **Multiple Game Modes**: Time Trial, Battle Mode, Checkpoint Race, Item Pickup, Free Drive
- **Car Customization**: Customize colors, decals, and unlock new vehicles
- **Touch Controls**: Simple controls for acceleration, braking, and steering

## 🛠️ Tech Stack

- **Unity 2022.3 LTS** - Game engine
- **AR Foundation 5.0+** - Cross-platform AR framework
- **ARCore** (Android) / **ARKit** (iOS) - Platform-specific AR
- **Photon PUN2** - Multiplayer networking
- **C#** - Programming language

## 📱 Target Platforms

- Android 7.0+ (API level 24+) with ARCore support
- iOS 11.0+ with ARKit support

## 🏗️ Project Structure

```
Assets/
├── Scripts/
│   ├── AR/              # AR-related scripts
│   ├── Car/             # Car controller and physics
│   ├── Multiplayer/     # Networking and synchronization
│   ├── GameModes/       # Different game mode implementations
│   ├── UI/              # User interface scripts
│   └── Managers/        # Game managers and singletons
├── Prefabs/
│   ├── Cars/            # Car prefabs
│   ├── Track/           # Track pieces and checkpoints
│   ├── UI/              # UI prefabs
│   └── Effects/         # Particle effects and audio
├── Materials/           # Materials and shaders
├── Textures/            # Textures and sprites
├── Audio/               # Sound effects and music
├── Scenes/              # Game scenes
└── StreamingAssets/     # Platform-specific assets
```

## 🚀 Getting Started

### 🌐 **Quick Web Demo** (No Unity Required!)

1. **Run Locally**:
   ```bash
   # Clone or download this repository
   git clone https://github.com/your-username/ar-car-racing.git
   cd ar-car-racing

   # Start the demo server (requires Node.js)
   npm start
   # OR
   node simple-server.js
   ```

2. **Access the Demo**:
   - Open http://localhost:3000 in your browser
   - Test WebXR support on mobile devices
   - Try the multiplayer connection test

### 📱 **Mobile AR Testing**:
   - Use Chrome or Edge on Android/iOS
   - Enable camera permissions
   - Works best on devices with ARCore/ARKit support

### 🏗️ **Full Unity Development**:

1. **Prerequisites**:
   - Unity 2022.3 LTS or newer
   - Node.js 14+ (for web server)
   - Android SDK (for Android builds)
   - Xcode (for iOS builds)
   - ARCore/ARKit compatible device for testing

2. **Setup**:
   - Clone this repository
   - Open the project in Unity
   - Install required packages via Package Manager
   - Configure build settings for your target platform

3. **Building**:
   - **WebGL**: Switch to WebGL platform, build to `Build/` folder
   - **Mobile**: Switch to Android/iOS platform, configure permissions
   - **Deploy**: Use provided deployment scripts

## 📋 Development Roadmap

- [x] Project Setup and Architecture
- [ ] AR Foundation Integration
- [ ] Core Car Controller System
- [ ] Track System Development
- [ ] Multiplayer Networking Foundation
- [ ] Shared AR Space Synchronization
- [ ] Game Modes Implementation
- [ ] UI and Game Flow
- [ ] Car Customization System
- [ ] Audio and Visual Polish
- [ ] Testing and Platform Optimization
- [ ] Build and Deployment

## 🎯 Game Modes

### Time Trial
Race against the clock to achieve the best lap time.

### Battle Mode
Bump into opponents to push them off the track while avoiding being pushed yourself.

### Checkpoint Race
Navigate through AR checkpoints in the correct order to complete the race.

### Item Pickup Mode
Collect power-ups and items scattered around the track for advantages.

### Free Drive
Explore and drive without rules or objectives.

## 🔧 Configuration

The game supports various configuration options through the Settings Manager:
- Graphics quality settings
- Audio volume controls
- Control sensitivity
- Multiplayer preferences

## 🌐 Web Hosting Options

### **Option 1: Netlify (Recommended - Free)**
1. Fork this repository on GitHub
2. Connect your GitHub account to Netlify
3. Deploy directly from GitHub
4. Automatic HTTPS and global CDN included
5. **Live Demo**: [ar-car-racing.netlify.app](https://ar-car-racing.netlify.app)

### **Option 2: Vercel (Free)**
1. Install Vercel CLI: `npm i -g vercel`
2. Run `vercel` in project directory
3. Follow the prompts to deploy
4. Automatic HTTPS and edge functions

### **Option 3: GitHub Pages (Free)**
1. Enable GitHub Pages in repository settings
2. Set source to main branch
3. Access at: `https://yourusername.github.io/ar-car-racing`

### **Option 4: Local Development**
```bash
# Start local server
npm start

# Access at http://localhost:3000
# For mobile testing, use your local IP
```

### **Option 5: Railway/Heroku (Free Tier)**
1. Connect GitHub repository
2. Deploy with automatic builds
3. Supports both static and Node.js hosting

## 📱 Mobile Access

For full AR functionality, access your hosted site from:
- **Android**: Chrome 79+ with ARCore
- **iOS**: Safari 13+ with ARKit (iOS 13+)
- **Ensure HTTPS** is enabled (required for WebXR)

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

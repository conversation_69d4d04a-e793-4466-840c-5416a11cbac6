<!DOCTYPE html>
<html lang="en-us">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>AR Car Racing - Web Edition</title>
    
    <!-- WebXR Polyfill -->
    <script src="https://cdn.jsdelivr.net/npm/webxr-polyfill@latest/build/webxr-polyfill.js"></script>
    
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow: hidden;
        }
        
        #unity-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
        }
        
        #unity-canvas {
            width: 100%;
            height: 100%;
            display: block;
            cursor: default;
        }
        
        #unity-loading-bar {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            display: none;
        }
        
        #unity-logo {
            width: 154px;
            height: 130px;
            background: url('unity-logo-dark.png') no-repeat center / contain;
        }
        
        #unity-progress-bar-empty {
            width: 141px;
            height: 18px;
            margin-top: 10px;
            background: url('progress-bar-empty-dark.png') no-repeat center / contain;
        }
        
        #unity-progress-bar-full {
            width: 0%;
            height: 18px;
            background: url('progress-bar-full-dark.png') no-repeat center / contain;
        }
        
        #unity-footer {
            position: relative;
            height: 38px;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }
        
        #unity-webgl-logo {
            float: left;
            width: 204px;
            height: 38px;
            background: url('webgl-logo.png') no-repeat center / contain;
        }
        
        #unity-build-title {
            float: right;
            margin-right: 10px;
            line-height: 38px;
            font-size: 18px;
        }
        
        #unity-fullscreen-button {
            float: right;
            width: 38px;
            height: 38px;
            background: url('fullscreen-button.png') no-repeat center / contain;
            cursor: pointer;
        }
        
        .unity-mobile #unity-container {
            width: 100%;
            height: 100%;
        }
        
        /* AR Controls */
        #ar-controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            display: flex;
            gap: 10px;
        }
        
        .ar-button {
            padding: 12px 24px;
            background: rgba(0, 123, 255, 0.9);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .ar-button:hover {
            background: rgba(0, 123, 255, 1);
            transform: translateY(-2px);
        }
        
        .ar-button:disabled {
            background: rgba(128, 128, 128, 0.5);
            cursor: not-allowed;
        }
        
        /* Mobile optimizations */
        @media (max-width: 768px) {
            #unity-footer {
                height: 50px;
                padding: 0 10px;
            }
            
            #unity-build-title {
                font-size: 14px;
            }
            
            .ar-button {
                padding: 10px 20px;
                font-size: 14px;
            }
        }
        
        /* Loading screen */
        #loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
        }
        
        #loading-screen h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-align: center;
        }
        
        #loading-screen p {
            font-size: 1.2em;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* WebXR not supported message */
        #webxr-not-supported {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 193, 7, 0.9);
            color: #856404;
            padding: 10px 20px;
            border-radius: 5px;
            display: none;
            z-index: 1001;
        }
        
        /* Instructions */
        #instructions {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            max-width: 300px;
            font-size: 14px;
            z-index: 1000;
        }
        
        #instructions h3 {
            margin-top: 0;
            color: #00bfff;
        }
        
        #instructions ul {
            margin: 10px 0;
            padding-left: 20px;
        }
    </style>
</head>

<body>
    <!-- Loading Screen -->
    <div id="loading-screen">
        <h1>🏎️ AR Car Racing</h1>
        <p>Loading your racing experience...</p>
        <div class="spinner"></div>
        <p id="loading-status">Initializing...</p>
    </div>
    
    <!-- WebXR Not Supported Message -->
    <div id="webxr-not-supported">
        ⚠️ WebXR not supported on this device. You can still play in 3D mode!
    </div>
    
    <!-- Instructions -->
    <div id="instructions">
        <h3>🎮 How to Play</h3>
        <ul>
            <li><strong>AR Mode:</strong> Click "Start AR" and point at a flat surface</li>
            <li><strong>3D Mode:</strong> Click anywhere to place the track</li>
            <li><strong>Controls:</strong> Use touch/mouse to steer and accelerate</li>
            <li><strong>Multiplayer:</strong> Share the room code with friends</li>
        </ul>
        <button onclick="hideInstructions()" style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">Got it!</button>
    </div>
    
    <!-- Unity Container -->
    <div id="unity-container" class="unity-desktop">
        <canvas id="unity-canvas" tabindex="-1"></canvas>
        <div id="unity-loading-bar">
            <div id="unity-logo"></div>
            <div id="unity-progress-bar-empty">
                <div id="unity-progress-bar-full"></div>
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <div id="unity-footer">
        <div id="unity-webgl-logo"></div>
        <div id="unity-fullscreen-button" onclick="unityInstance.SetFullscreen(1)"></div>
        <div id="unity-build-title">AR Car Racing</div>
    </div>
    
    <!-- AR Controls -->
    <div id="ar-controls">
        <button id="start-ar-btn" class="ar-button" onclick="startAR()">🥽 Start AR</button>
        <button id="place-track-btn" class="ar-button" onclick="placeTrack()" style="display: none;">📍 Place Track</button>
        <button id="join-room-btn" class="ar-button" onclick="showJoinRoom()">👥 Join Room</button>
        <button id="fullscreen-btn" class="ar-button" onclick="toggleFullscreen()">⛶ Fullscreen</button>
    </div>
    
    <!-- Unity WebGL Script -->
    <script>
        var unityInstance = null;
        var isARSupported = false;
        var isARActive = false;
        
        // Check WebXR support
        function checkWebXRSupport() {
            if (navigator.xr) {
                navigator.xr.isSessionSupported('immersive-ar').then(function(supported) {
                    isARSupported = supported;
                    if (!supported) {
                        document.getElementById('webxr-not-supported').style.display = 'block';
                        document.getElementById('start-ar-btn').textContent = '🎮 3D Mode';
                    }
                }).catch(function() {
                    isARSupported = false;
                    document.getElementById('webxr-not-supported').style.display = 'block';
                    document.getElementById('start-ar-btn').textContent = '🎮 3D Mode';
                });
            } else {
                document.getElementById('webxr-not-supported').style.display = 'block';
                document.getElementById('start-ar-btn').textContent = '🎮 3D Mode';
            }
        }
        
        // Start AR or 3D mode
        function startAR() {
            if (unityInstance) {
                if (isARSupported) {
                    unityInstance.SendMessage('WebXRManager', 'StartWebXRSession', '');
                    isARActive = true;
                    document.getElementById('start-ar-btn').textContent = '🛑 Stop AR';
                    document.getElementById('start-ar-btn').onclick = stopAR;
                } else {
                    // Fallback to 3D mode
                    unityInstance.SendMessage('WebXRManager', 'ShowWebXRInstructions', '');
                }
            }
        }
        
        // Stop AR
        function stopAR() {
            if (unityInstance && isARActive) {
                unityInstance.SendMessage('WebXRManager', 'EndWebXRSession', '');
                isARActive = false;
                document.getElementById('start-ar-btn').textContent = '🥽 Start AR';
                document.getElementById('start-ar-btn').onclick = startAR;
            }
        }
        
        // Place track
        function placeTrack() {
            if (unityInstance) {
                // This would trigger track placement in Unity
                console.log('Place track requested');
            }
        }
        
        // Show join room dialog
        function showJoinRoom() {
            var roomCode = prompt('Enter room code to join multiplayer:');
            if (roomCode && unityInstance) {
                unityInstance.SendMessage('WebMultiplayerManager', 'JoinMultiplayerRoom', roomCode);
            }
        }
        
        // Toggle fullscreen
        function toggleFullscreen() {
            if (unityInstance) {
                unityInstance.SetFullscreen(1);
            }
        }
        
        // Hide instructions
        function hideInstructions() {
            document.getElementById('instructions').style.display = 'none';
        }
        
        // Update loading status
        function updateLoadingStatus(status) {
            document.getElementById('loading-status').textContent = status;
        }
        
        // Hide loading screen
        function hideLoadingScreen() {
            document.getElementById('loading-screen').style.display = 'none';
        }
        
        // Unity configuration
        var buildUrl = "Build";
        var loaderUrl = buildUrl + "/{{{ LOADER_FILENAME }}}";
        var config = {
            dataUrl: buildUrl + "/{{{ DATA_FILENAME }}}",
            frameworkUrl: buildUrl + "/{{{ FRAMEWORK_FILENAME }}}",
            codeUrl: buildUrl + "/{{{ CODE_FILENAME }}}",
#if MEMORY_FILENAME
            memoryUrl: buildUrl + "/{{{ MEMORY_FILENAME }}}",
#endif
#if SYMBOLS_FILENAME
            symbolsUrl: buildUrl + "/{{{ SYMBOLS_FILENAME }}}",
#endif
            streamingAssetsUrl: "StreamingAssets",
            companyName: "{{{ COMPANY_NAME }}}",
            productName: "{{{ PRODUCT_NAME }}}",
            productVersion: "{{{ PRODUCT_VERSION }}}",
        };
        
        // Mobile detection
        if (/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)) {
            var meta = document.createElement('meta');
            meta.name = 'viewport';
            meta.content = 'width=device-width, height=device-height, initial-scale=1.0, user-scalable=no, shrink-to-fit=yes';
            document.getElementsByTagName('head')[0].appendChild(meta);
            document.querySelector("#unity-container").className = "unity-mobile";
            config.devicePixelRatio = 1;
        }
        
        // Loading progress
        document.querySelector("#unity-loading-bar").style.display = "block";
        
        // Load Unity
        updateLoadingStatus('Loading Unity...');
        var script = document.createElement("script");
        script.src = loaderUrl;
        script.onload = () => {
            updateLoadingStatus('Creating Unity instance...');
            createUnityInstance(document.querySelector("#unity-canvas"), config, (progress) => {
                document.querySelector("#unity-progress-bar-full").style.width = 100 * progress + "%";
                updateLoadingStatus('Loading: ' + Math.round(progress * 100) + '%');
            }).then((instance) => {
                unityInstance = instance;
                document.querySelector("#unity-loading-bar").style.display = "none";
                hideLoadingScreen();
                checkWebXRSupport();
                updateLoadingStatus('Ready!');
            }).catch((message) => {
                alert(message);
            });
        };
        document.body.appendChild(script);
        
        // Handle page visibility changes
        document.addEventListener('visibilitychange', function() {
            if (unityInstance) {
                if (document.hidden) {
                    unityInstance.SendMessage('WebBuildManager', 'OnApplicationPause', 'true');
                } else {
                    unityInstance.SendMessage('WebBuildManager', 'OnApplicationPause', 'false');
                }
            }
        });
        
        // Handle window resize
        window.addEventListener('resize', function() {
            if (unityInstance) {
                // Unity will handle this automatically
            }
        });
    </script>
</body>
</html>

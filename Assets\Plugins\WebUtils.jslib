// Web utilities JavaScript plugin for Unity WebGL
var WebUtilsPlugin = {
    // Browser detection
    GetBrowserInfo: function() {
        var userAgent = navigator.userAgent;
        var browserName = "Unknown";
        var browserVersion = "Unknown";
        
        // Detect browser
        if (userAgent.indexOf("Chrome") > -1) {
            browserName = "Chrome";
            var match = userAgent.match(/Chrome\/(\d+)/);
            if (match) browserVersion = match[1];
        } else if (userAgent.indexOf("Firefox") > -1) {
            browserName = "Firefox";
            var match = userAgent.match(/Firefox\/(\d+)/);
            if (match) browserVersion = match[1];
        } else if (userAgent.indexOf("Safari") > -1) {
            browserName = "Safari";
            var match = userAgent.match(/Version\/(\d+)/);
            if (match) browserVersion = match[1];
        } else if (userAgent.indexOf("Edge") > -1) {
            browserName = "Edge";
            var match = userAgent.match(/Edge\/(\d+)/);
            if (match) browserVersion = match[1];
        }
        
        var result = browserName + "/" + browserVersion;
        var bufferSize = lengthBytesUTF8(result) + 1;
        var buffer = _malloc(bufferSize);
        stringToUTF8(result, buffer, bufferSize);
        return buffer;
    },
    
    // Mobile detection
    IsMobile: function() {
        var userAgent = navigator.userAgent;
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
    },
    
    // Fullscreen functionality
    RequestFullscreen: function() {
        var canvas = document.querySelector('canvas');
        if (canvas) {
            if (canvas.requestFullscreen) {
                canvas.requestFullscreen();
            } else if (canvas.webkitRequestFullscreen) {
                canvas.webkitRequestFullscreen();
            } else if (canvas.mozRequestFullScreen) {
                canvas.mozRequestFullScreen();
            } else if (canvas.msRequestFullscreen) {
                canvas.msRequestFullscreen();
            }
        }
    },
    
    ExitFullscreen: function() {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
        } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
        }
    },
    
    IsFullscreen: function() {
        return !!(document.fullscreenElement || 
                 document.webkitFullscreenElement || 
                 document.mozFullScreenElement || 
                 document.msFullscreenElement);
    },
    
    // Canvas size management
    SetCanvasSize: function(width, height) {
        var canvas = document.querySelector('canvas');
        if (canvas) {
            canvas.style.width = width + 'px';
            canvas.style.height = height + 'px';
            
            // Also update the actual canvas resolution
            canvas.width = width;
            canvas.height = height;
        }
    },
    
    // Loading screen management
    ShowLoadingScreen: function(messagePtr) {
        var message = UTF8ToString(messagePtr);
        
        // Create loading overlay if it doesn't exist
        var loadingDiv = document.getElementById('unity-loading');
        if (!loadingDiv) {
            loadingDiv = document.createElement('div');
            loadingDiv.id = 'unity-loading';
            loadingDiv.style.position = 'fixed';
            loadingDiv.style.top = '0';
            loadingDiv.style.left = '0';
            loadingDiv.style.width = '100%';
            loadingDiv.style.height = '100%';
            loadingDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
            loadingDiv.style.display = 'flex';
            loadingDiv.style.justifyContent = 'center';
            loadingDiv.style.alignItems = 'center';
            loadingDiv.style.zIndex = '9999';
            loadingDiv.style.color = 'white';
            loadingDiv.style.fontSize = '24px';
            loadingDiv.style.fontFamily = 'Arial, sans-serif';
            
            document.body.appendChild(loadingDiv);
        }
        
        loadingDiv.innerHTML = '<div>' + message + '<br><div class="spinner"></div></div>';
        loadingDiv.style.display = 'flex';
        
        // Add spinner CSS if not already added
        if (!document.getElementById('spinner-style')) {
            var style = document.createElement('style');
            style.id = 'spinner-style';
            style.textContent = `
                .spinner {
                    border: 4px solid #f3f3f3;
                    border-top: 4px solid #3498db;
                    border-radius: 50%;
                    width: 40px;
                    height: 40px;
                    animation: spin 2s linear infinite;
                    margin: 20px auto;
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }
    },
    
    HideLoadingScreen: function() {
        var loadingDiv = document.getElementById('unity-loading');
        if (loadingDiv) {
            loadingDiv.style.display = 'none';
        }
    },
    
    // Performance monitoring
    GetPerformanceInfo: function() {
        var info = {
            memory: 0,
            fps: 0,
            ping: 0
        };
        
        // Get memory info if available
        if (performance.memory) {
            info.memory = performance.memory.usedJSHeapSize / 1024 / 1024; // MB
        }
        
        // Get FPS (approximate)
        if (window.fpsCounter) {
            info.fps = window.fpsCounter;
        }
        
        var result = JSON.stringify(info);
        var bufferSize = lengthBytesUTF8(result) + 1;
        var buffer = _malloc(bufferSize);
        stringToUTF8(result, buffer, bufferSize);
        return buffer;
    },
    
    // Local storage functions
    SaveToLocalStorage: function(keyPtr, valuePtr) {
        var key = UTF8ToString(keyPtr);
        var value = UTF8ToString(valuePtr);
        
        try {
            localStorage.setItem(key, value);
            return true;
        } catch (e) {
            console.error('Failed to save to localStorage:', e);
            return false;
        }
    },
    
    LoadFromLocalStorage: function(keyPtr) {
        var key = UTF8ToString(keyPtr);
        
        try {
            var value = localStorage.getItem(key);
            if (value === null) return null;
            
            var bufferSize = lengthBytesUTF8(value) + 1;
            var buffer = _malloc(bufferSize);
            stringToUTF8(value, buffer, bufferSize);
            return buffer;
        } catch (e) {
            console.error('Failed to load from localStorage:', e);
            return null;
        }
    },
    
    // URL parameters
    GetURLParameter: function(paramPtr) {
        var param = UTF8ToString(paramPtr);
        var urlParams = new URLSearchParams(window.location.search);
        var value = urlParams.get(param);
        
        if (value === null) return null;
        
        var bufferSize = lengthBytesUTF8(value) + 1;
        var buffer = _malloc(bufferSize);
        stringToUTF8(value, buffer, bufferSize);
        return buffer;
    },
    
    // Clipboard functionality
    CopyToClipboard: function(textPtr) {
        var text = UTF8ToString(textPtr);
        
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(text).then(function() {
                console.log('Text copied to clipboard');
            }).catch(function(err) {
                console.error('Failed to copy text: ', err);
            });
        } else {
            // Fallback for older browsers
            var textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
        }
    },
    
    // Device orientation (for mobile)
    GetDeviceOrientation: function() {
        if (screen.orientation) {
            var orientation = screen.orientation.angle;
            return orientation;
        } else if (window.orientation !== undefined) {
            return window.orientation;
        }
        return 0;
    },
    
    // Network status
    IsOnline: function() {
        return navigator.onLine;
    },
    
    // Battery status (if available)
    GetBatteryLevel: function() {
        if (navigator.getBattery) {
            navigator.getBattery().then(function(battery) {
                return battery.level * 100;
            });
        }
        return -1; // Not available
    },
    
    // Vibration (for mobile)
    Vibrate: function(duration) {
        if (navigator.vibrate) {
            navigator.vibrate(duration);
        }
    },
    
    // Share API (for mobile)
    ShareContent: function(titlePtr, textPtr, urlPtr) {
        var title = UTF8ToString(titlePtr);
        var text = UTF8ToString(textPtr);
        var url = UTF8ToString(urlPtr);
        
        if (navigator.share) {
            navigator.share({
                title: title,
                text: text,
                url: url
            }).then(function() {
                console.log('Content shared successfully');
            }).catch(function(err) {
                console.error('Error sharing content:', err);
            });
        } else {
            // Fallback - copy URL to clipboard
            WebUtilsPlugin.CopyToClipboard(url);
        }
    }
};

// FPS counter
(function() {
    var fps = 0;
    var lastTime = performance.now();
    var frameCount = 0;
    
    function updateFPS() {
        frameCount++;
        var currentTime = performance.now();
        
        if (currentTime - lastTime >= 1000) {
            fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
            window.fpsCounter = fps;
            frameCount = 0;
            lastTime = currentTime;
        }
        
        requestAnimationFrame(updateFPS);
    }
    
    updateFPS();
})();

// Export functions for Unity
mergeInto(LibraryManager.library, WebUtilsPlugin);

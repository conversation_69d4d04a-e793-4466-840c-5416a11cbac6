%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!129 &1
PlayerSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 24
  productGUID: 12345678901234567890123456789012
  AndroidProfiler: 0
  AndroidFilterTouchesWhenObscured: 0
  AndroidEnableSustainedPerformanceMode: 0
  defaultScreenOrientation: 3
  targetDevice: 2
  useOnDemandResources: 0
  accelerometerFrequency: 60
  companyName: ARCarRacing
  productName: AR Car Racing
  defaultCursor: {fileID: 0}
  cursorHotspot: {x: 0, y: 0}
  m_SplashScreenBackgroundColor: {r: 0.13725491, g: 0.12156863, b: 0.1254902, a: 1}
  m_ShowUnitySplashScreen: 1
  m_ShowUnitySplashLogo: 1
  m_SplashScreenOverlayOpacity: 1
  m_SplashScreenAnimation: 1
  m_SplashScreenLogoStyle: 1
  m_SplashScreenDrawMode: 0
  m_SplashScreenBackgroundAnimationZoom: 1
  m_SplashScreenLogoAnimationZoom: 1
  m_SplashScreenBackgroundLandscapeAspectRatio: 2
  m_SplashScreenBackgroundPortraitAspectRatio: 1
  m_SplashScreenBackgroundLandscapeUvs:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  m_SplashScreenBackgroundPortraitUvs:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  m_SplashScreenLogos: []
  m_VirtualRealitySDKs: []
  m_TargetPixelDensity: 30
  m_IOSHighResolutionPortraitSplashScreen: {fileID: 0}
  m_IOSHighResolutionLandscapeSplashScreen: {fileID: 0}
  m_IOSLaunchScreenType: 0
  m_IOSLaunchScreenPortrait: {fileID: 0}
  m_IOSLaunchScreenLandscape: {fileID: 0}
  m_IOSLaunchScreenBackgroundColor:
    serializedVersion: 2
    rgba: 0
  m_IOSLaunchScreenFillPct: 100
  m_IOSLaunchScreenSize: 100
  m_IOSLaunchScreenCustomXibPath: 
  m_IOSLaunchScreeniPadType: 0
  m_IOSLaunchScreeniPadImage: {fileID: 0}
  m_IOSLaunchScreeniPadBackgroundColor:
    serializedVersion: 2
    rgba: 0
  m_IOSLaunchScreeniPadFillPct: 100
  m_IOSLaunchScreeniPadSize: 100
  m_IOSLaunchScreeniPadCustomXibPath: 
  m_IOSUseLaunchScreenStoryboard: 0
  m_IOSLaunchScreenCustomStoryboardPath: 
  m_IOSDeviceRequirements: []
  m_IOSURLSchemes: []
  m_IOSBackgroundModes: 0
  m_IOSMetalForceHardShadows: 0
  m_IOSDebugOverlayConfig: 63
  m_AndroidTargetArchitectures: 3
  m_AndroidSplashScreenScale: 0
  m_androidSplashScreen: {fileID: 0}
  m_AndroidKeystoreName: 
  m_AndroidKeyaliasName: 
  m_AndroidBuildApkPerCpuArchitecture: 0
  m_AndroidTVCompatibility: 0
  m_AndroidIsGame: 1
  m_AndroidEnableTango: 0
  m_androidEnableBanner: 1
  m_androidUseLowAccuracyLocation: 0
  m_androidUseCustomKeystore: 0
  m_AndroidBundleVersionCode: 1
  m_AndroidMinSdkVersion: 24
  m_AndroidTargetSdkVersion: 0
  m_AndroidPreferredInstallLocation: 1
  m_aotOptions: 
  m_stripEngineCode: 1
  m_iPhoneStrippingLevel: 0
  m_iPhoneScriptCallOptimization: 0
  m_ForceInternetPermission: 1
  m_ForceSDCardPermission: 0
  m_CreateWallpaper: 0
  m_APKExpansionFiles: 0
  m_keepLoadedShadersAlive: 0
  m_StripUnusedMeshComponents: 1
  m_VertexChannelCompressionMask: 4054
  m_iPhoneSdkVersion: 988
  m_iOSTargetOSVersionString: 11.0
  m_tvOSSdkVersion: 0
  m_tvOSRequireExtendedGameController: 0
  m_tvOSTargetOSVersionString: 11.0
  m_uIPrerenderedIcon: 0
  m_uIRequiresPersistentWiFi: 0
  m_uIRequiresFullScreen: 1
  m_uIStatusBarHidden: 1
  m_uIExitOnSuspend: 0
  m_uIStatusBarStyle: 0
  m_iPhoneSplashScreen: {fileID: 0}
  m_iPhoneHighResSplashScreen: {fileID: 0}
  m_iPhoneTallHighResSplashScreen: {fileID: 0}
  m_iPhone47inSplashScreen: {fileID: 0}
  m_iPhone55inPortraitSplashScreen: {fileID: 0}
  m_iPhone55inLandscapeSplashScreen: {fileID: 0}
  m_iPhone58inPortraitSplashScreen: {fileID: 0}
  m_iPhone58inLandscapeSplashScreen: {fileID: 0}
  m_iPadPortraitSplashScreen: {fileID: 0}
  m_iPadHighResPortraitSplashScreen: {fileID: 0}
  m_iPadLandscapeSplashScreen: {fileID: 0}
  m_iPadHighResLandscapeSplashScreen: {fileID: 0}
  m_appleTVSplashScreen: {fileID: 0}
  m_appleTVSplashScreen2x: {fileID: 0}
  m_tvOSSmallIconLayers: []
  m_tvOSSmallIconLayers2x: []
  m_tvOSLargeIconLayers: []
  m_tvOSLargeIconLayers2x: []
  m_tvOSTopShelfImageLayers: []
  m_tvOSTopShelfImageLayers2x: []
  m_tvOSTopShelfImageWideLayers: []
  m_tvOSTopShelfImageWideLayers2x: []
  m_iOSLaunchScreeniPadCustomPropertyList: 
  m_iOSLaunchScreeniPhoneCustomPropertyList: 
  m_iOSDeviceFamily: 1
  m_iOSLaunchScreenCustomPropertyList: 
  m_iOSAppInBackgroundBehavior: 0
  m_iOSAllowHTTPDownload: 1
  m_allowedAutorotateToPortrait: 1
  m_allowedAutorotateToPortraitUpsideDown: 1
  m_allowedAutorotateToLandscapeRight: 1
  m_allowedAutorotateToLandscapeLeft: 1
  m_useOSAutorotation: 1
  m_use32BitDisplayBuffer: 1
  m_preserveFramebufferAlpha: 0
  m_disableDepthAndStencilBuffers: 0
  m_androidStartInFullscreen: 1
  m_androidRenderOutsideSafeArea: 1
  m_androidUseSwappy: 1
  m_androidBlitType: 0
  m_androidResizableWindow: 0
  m_androidDefaultWindowWidth: 1920
  m_androidDefaultWindowHeight: 1080
  m_androidMinimumWindowWidth: 400
  m_androidMinimumWindowHeight: 300
  m_androidFullscreenMode: 1
  m_defaultIsNativeResolution: 1
  m_macRetinaSupport: 1
  m_runInBackground: 1
  m_captureSingleScreen: 0
  m_muteOtherAudioSources: 0
  m_Prepare: 0
  m_Override: 0
  m_defaultScreenWidth: 1920
  m_defaultScreenHeight: 1080
  m_defaultScreenWidthWeb: 960
  m_defaultScreenHeightWeb: 600
  m_colorSpace: 0
  m_MTRendering: 1
  m_StackTraceTypes: 010000000100000001000000010000000100000001000000
  iosShowActivityIndicatorOnLoading: -1
  androidShowActivityIndicatorOnLoading: -1
  iosUseCustomAppBackgroundBehavior: 0
  iosAllowHTTPDownload: 1
  allowedAutorotateToPortrait: 1
  allowedAutorotateToPortraitUpsideDown: 1
  allowedAutorotateToLandscapeRight: 1
  allowedAutorotateToLandscapeLeft: 1
  useOSAutorotation: 1
  use32BitDisplayBuffer: 1
  preserveFramebufferAlpha: 0
  disableDepthAndStencilBuffers: 0
  androidStartInFullscreen: 1
  androidRenderOutsideSafeArea: 1
  androidUseSwappy: 1
  androidBlitType: 0
  androidResizableWindow: 0
  androidDefaultWindowWidth: 1920
  androidDefaultWindowHeight: 1080
  androidMinimumWindowWidth: 400
  androidMinimumWindowHeight: 300
  androidFullscreenMode: 1
  defaultIsNativeResolution: 1
  macRetinaSupport: 1
  runInBackground: 0
  captureSingleScreen: 0
  muteOtherAudioSources: 0
  Prepare: 0
  Override: 0
  defaultScreenWidth: 1920
  defaultScreenHeight: 1080
  defaultScreenWidthWeb: 960
  defaultScreenHeightWeb: 600
  colorSpace: 0
  MTRendering: 1
  StackTraceTypes: 010000000100000001000000010000000100000001000000
  iosShowActivityIndicatorOnLoading: -1
  androidShowActivityIndicatorOnLoading: -1
  tizenShowActivityIndicatorOnLoading: -1
  iosUseCustomAppBackgroundBehavior: 0
  bundleVersion: 1.0
  preloadedAssets: []
  metroInputSource: 0
  wsaTransparentSwapchain: 0
  m_HolographicPauseOnTrackingLoss: 1
  xboxOneDisableKinectGpuReservation: 1
  xboxOneEnable7thCore: 1
  vrSettings:
    cardboard:
      depthFormat: 0
      enableTransitionView: 0
    daydream:
      depthFormat: 0
      useSustainedPerformanceMode: 0
      enableVideoLayer: 0
      useProtectedVideoMemory: 0
      minimumSupportedHeadTracking: 0
      maximumSupportedHeadTracking: 1
    hololens:
      depthFormat: 1
      depthBufferSharingEnabled: 1
    lumin:
      depthFormat: 0
      frameTiming: 2
      enableGLCache: 0
      glCacheMaxBlobSize: 524288
      glCacheMaxFileSize: 8388608
    oculus:
      sharedDepthBuffer: 1
      dashSupport: 1
      lowOverheadMode: 0
      protectedContext: 0
      v2Signing: 1
    enable360StereoCapture: 0
  isWsaHolographicRemotingEnabled: 0
  enableFrameTimingStats: 0
  useHDRDisplay: 0
  D3DHDRBitDepth: 0
  m_ColorGamuts: 00000000
  targetPixelDensity: 30
  resolutionScalingMode: 0
  androidSupportedAspectRatio: 1
  androidMaxAspectRatio: 2.1
  applicationIdentifier:
    Android: com.arcarracing.game
    iOS: com.arcarracing.game
  buildNumber:
    Standalone: 0
    iPhone: 0
    tvOS: 0
  overrideDefaultApplicationIdentifier: 1
  AndroidBundleVersionCode: 1
  AndroidMinSdkVersion: 24
  AndroidTargetSdkVersion: 0
  AndroidPreferredInstallLocation: 1
  aotOptions: 
  stripEngineCode: 1
  iPhoneStrippingLevel: 0
  iPhoneScriptCallOptimization: 0
  ForceInternetPermission: 1
  ForceSDCardPermission: 0
  CreateWallpaper: 0
  APKExpansionFiles: 0
  keepLoadedShadersAlive: 0
  StripUnusedMeshComponents: 1
  VertexChannelCompressionMask: 4054
  iPhoneSdkVersion: 988
  iOSTargetOSVersionString: 11.0
  tvOSSdkVersion: 0
  tvOSRequireExtendedGameController: 0
  tvOSTargetOSVersionString: 11.0
  uIPrerenderedIcon: 0
  uIRequiresPersistentWiFi: 0
  uIRequiresFullScreen: 1
  uIStatusBarHidden: 1
  uIExitOnSuspend: 0
  uIStatusBarStyle: 0
  iPhoneSplashScreen: {fileID: 0}
  iPhoneHighResSplashScreen: {fileID: 0}
  iPhoneTallHighResSplashScreen: {fileID: 0}
  iPhone47inSplashScreen: {fileID: 0}
  iPhone55inPortraitSplashScreen: {fileID: 0}
  iPhone55inLandscapeSplashScreen: {fileID: 0}
  iPhone58inPortraitSplashScreen: {fileID: 0}
  iPhone58inLandscapeSplashScreen: {fileID: 0}
  iPadPortraitSplashScreen: {fileID: 0}
  iPadHighResPortraitSplashScreen: {fileID: 0}
  iPadLandscapeSplashScreen: {fileID: 0}
  iPadHighResLandscapeSplashScreen: {fileID: 0}
  appleTVSplashScreen: {fileID: 0}
  appleTVSplashScreen2x: {fileID: 0}
  tvOSSmallIconLayers: []
  tvOSSmallIconLayers2x: []
  tvOSLargeIconLayers: []
  tvOSLargeIconLayers2x: []
  tvOSTopShelfImageLayers: []
  tvOSTopShelfImageLayers2x: []
  tvOSTopShelfImageWideLayers: []
  tvOSTopShelfImageWideLayers2x: []
  iOSLaunchScreeniPadCustomPropertyList: 
  iOSLaunchScreeniPhoneCustomPropertyList: 
  iOSDeviceFamily: 1
  iOSLaunchScreenCustomPropertyList: 
  iOSAppInBackgroundBehavior: 0
  iOSAllowHTTPDownload: 1
  allowedAutorotateToPortrait: 1
  allowedAutorotateToPortraitUpsideDown: 1
  allowedAutorotateToLandscapeRight: 1
  allowedAutorotateToLandscapeLeft: 1
  useOSAutorotation: 1
  use32BitDisplayBuffer: 1
  preserveFramebufferAlpha: 0
  disableDepthAndStencilBuffers: 0
  androidStartInFullscreen: 1
  androidRenderOutsideSafeArea: 1
  androidUseSwappy: 1
  androidBlitType: 0
  androidResizableWindow: 0
  androidDefaultWindowWidth: 1920
  androidDefaultWindowHeight: 1080
  androidMinimumWindowWidth: 400
  androidMinimumWindowHeight: 300
  androidFullscreenMode: 1
  defaultIsNativeResolution: 1
  macRetinaSupport: 1
  runInBackground: 0
  captureSingleScreen: 0
  muteOtherAudioSources: 0
  Prepare: 0
  Override: 0
  defaultScreenWidth: 1920
  defaultScreenHeight: 1080
  defaultScreenWidthWeb: 960
  defaultScreenHeightWeb: 600
  colorSpace: 0
  MTRendering: 1

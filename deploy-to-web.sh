#!/bin/bash

echo "🚀 AR Car Racing - Web Deployment Script"
echo "========================================"

echo "Checking prerequisites..."

# Check if Python is available
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "❌ Python not found. Please install Python from https://python.org/"
    echo "   Alternative: Use the Node.js server if you have Node.js installed"
    exit 1
fi

echo "✅ Python found!"

# Check if demo directory exists
if [ ! -d "demo" ]; then
    echo "❌ Demo directory not found!"
    echo "   Make sure you're in the project root directory"
    exit 1
fi

echo "✅ Demo files found!"

echo ""
echo "🌐 Starting local web server..."
echo "==============================="
echo ""
echo "📡 Server will be available at:"
echo "   • Local: http://localhost:3000"
echo "   • Network: http://$(hostname -I | awk '{print $1}'):3000"
echo ""
echo "📱 For mobile AR testing:"
echo "   1. Connect mobile device to same WiFi"
echo "   2. Open Chrome/Edge on mobile"
echo "   3. Visit http://$(hostname -I | awk '{print $1}'):3000"
echo "   4. Allow camera permissions"
echo ""
echo "🔒 Note: Full AR features require HTTPS"
echo "   For production, deploy to Netlify/Vercel"
echo ""
echo "🛑 Press Ctrl+C to stop the server"
echo ""

# Start the Python server
if command -v python3 &> /dev/null; then
    python3 simple-python-server.py
else
    python simple-python-server.py
fi

echo ""
echo "👋 Server stopped. Thanks for using AR Car Racing!"

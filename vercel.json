{"version": 2, "name": "ar-car-racing", "builds": [{"src": "simple-server.js", "use": "@vercel/node"}], "routes": [{"src": "/", "dest": "/demo/index.html"}, {"src": "/demo", "dest": "/demo/index.html"}, {"src": "/health", "dest": "/simple-server.js"}, {"src": "/(.*)", "dest": "/$1"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Permissions-Policy", "value": "camera=*, microphone=*, geolocation=*, gyroscope=*, accelerometer=*, magnetometer=*"}]}, {"source": "/demo/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=3600"}]}, {"source": "/(.*)\\.wasm$", "headers": [{"key": "Content-Type", "value": "application/wasm"}, {"key": "Cache-Control", "value": "public, max-age=31536000"}]}, {"source": "/(.*)\\.data$", "headers": [{"key": "Content-Type", "value": "application/octet-stream"}, {"key": "Cache-Control", "value": "public, max-age=31536000"}]}], "functions": {"simple-server.js": {"maxDuration": 30}}}
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Runtime.InteropServices;

namespace ARCarRacing.Managers
{
    /// <summary>
    /// Manages web-specific build configurations and optimizations
    /// </summary>
    public class WebBuildManager : MonoBehaviour
    {
        [Header("Web Build Settings")]
        [SerializeField] private bool optimizeForWeb = true;
        [SerializeField] private bool enableWebXR = true;
        [SerializeField] private bool enableWebGL2 = true;
        [SerializeField] private int targetFrameRate = 60;
        
        [Header("Performance Settings")]
        [SerializeField] private bool enableGPUInstancing = false;
        [SerializeField] private bool enableBatching = true;
        [SerializeField] private int maxTextureSize = 1024;
        [SerializeField] private bool compressTextures = true;
        
        [Header("Web Features")]
        [SerializeField] private bool enableFullscreen = true;
        [SerializeField] private bool enablePointerLock = false;
        [SerializeField] private bool enableClipboard = false;
        
        // Singleton instance
        public static WebBuildManager Instance { get; private set; }
        
        // Web platform detection
        public bool IsWebGL => Application.platform == RuntimePlatform.WebGLPlayer;
        public bool IsRunningInBrowser => IsWebGL;
        
        // Browser information
        public string BrowserName { get; private set; } = "Unknown";
        public string BrowserVersion { get; private set; } = "Unknown";
        public bool IsMobileBrowser { get; private set; } = false;
        
        // JavaScript interface
        #if UNITY_WEBGL && !UNITY_EDITOR
        [DllImport("__Internal")]
        private static extern string GetBrowserInfo();
        
        [DllImport("__Internal")]
        private static extern bool IsMobile();
        
        [DllImport("__Internal")]
        private static extern void RequestFullscreen();
        
        [DllImport("__Internal")]
        private static extern void ExitFullscreen();
        
        [DllImport("__Internal")]
        private static extern bool IsFullscreen();
        
        [DllImport("__Internal")]
        private static extern void SetCanvasSize(int width, int height);
        
        [DllImport("__Internal")]
        private static extern void ShowLoadingScreen(string message);
        
        [DllImport("__Internal")]
        private static extern void HideLoadingScreen();
        #else
        private static string GetBrowserInfo() { return "Unity Editor"; }
        private static bool IsMobile() { return false; }
        private static void RequestFullscreen() { }
        private static void ExitFullscreen() { }
        private static bool IsFullscreen() { return false; }
        private static void SetCanvasSize(int width, int height) { }
        private static void ShowLoadingScreen(string message) { }
        private static void HideLoadingScreen() { }
        #endif
        
        private void Awake()
        {
            // Singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeWebBuild();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void InitializeWebBuild()
        {
            if (IsWebGL)
            {
                Debug.Log("Initializing WebGL build...");
                
                // Get browser information
                GetBrowserInformation();
                
                // Apply web optimizations
                if (optimizeForWeb)
                {
                    ApplyWebOptimizations();
                }
                
                // Set target frame rate
                Application.targetFrameRate = targetFrameRate;
                
                // Disable screen sleep
                Screen.sleepTimeout = SleepTimeout.NeverSleep;
                
                Debug.Log($"WebGL build initialized. Browser: {BrowserName}, Mobile: {IsMobileBrowser}");
            }
        }
        
        private void GetBrowserInformation()
        {
            #if UNITY_WEBGL && !UNITY_EDITOR
            try
            {
                string browserInfo = GetBrowserInfo();
                ParseBrowserInfo(browserInfo);
                IsMobileBrowser = IsMobile();
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"Failed to get browser info: {e.Message}");
            }
            #endif
        }
        
        private void ParseBrowserInfo(string browserInfo)
        {
            // Parse browser info string (format: "BrowserName/Version")
            if (!string.IsNullOrEmpty(browserInfo))
            {
                string[] parts = browserInfo.Split('/');
                if (parts.Length >= 2)
                {
                    BrowserName = parts[0];
                    BrowserVersion = parts[1];
                }
            }
        }
        
        private void ApplyWebOptimizations()
        {
            // Quality settings for web
            QualitySettings.SetQualityLevel(2); // Medium quality
            
            // Rendering optimizations
            QualitySettings.shadows = ShadowQuality.Disable;
            QualitySettings.shadowResolution = ShadowResolution.Low;
            QualitySettings.shadowDistance = 20f;
            
            // Texture optimizations
            QualitySettings.masterTextureLimit = 1; // Half resolution
            QualitySettings.anisotropicFiltering = AnisotropicFiltering.Disable;
            
            // Performance optimizations
            QualitySettings.vSyncCount = 0; // Disable VSync for better performance
            QualitySettings.antiAliasing = 0; // Disable anti-aliasing
            
            // Physics optimizations
            Time.fixedDeltaTime = 1f / 30f; // 30 FPS physics
            
            Debug.Log("Web optimizations applied");
        }
        
        public void RequestFullscreenMode()
        {
            if (!IsWebGL || !enableFullscreen) return;
            
            #if UNITY_WEBGL && !UNITY_EDITOR
            RequestFullscreen();
            #endif
            
            Debug.Log("Requested fullscreen mode");
        }
        
        public void ExitFullscreenMode()
        {
            if (!IsWebGL) return;
            
            #if UNITY_WEBGL && !UNITY_EDITOR
            ExitFullscreen();
            #endif
            
            Debug.Log("Exited fullscreen mode");
        }
        
        public bool IsInFullscreen()
        {
            if (!IsWebGL) return false;
            
            #if UNITY_WEBGL && !UNITY_EDITOR
            return IsFullscreen();
            #else
            return false;
            #endif
        }
        
        public void SetWebCanvasSize(int width, int height)
        {
            if (!IsWebGL) return;
            
            #if UNITY_WEBGL && !UNITY_EDITOR
            SetCanvasSize(width, height);
            #endif
            
            Debug.Log($"Set canvas size: {width}x{height}");
        }
        
        public void ShowWebLoadingScreen(string message = "Loading...")
        {
            if (!IsWebGL) return;
            
            #if UNITY_WEBGL && !UNITY_EDITOR
            ShowLoadingScreen(message);
            #endif
        }
        
        public void HideWebLoadingScreen()
        {
            if (!IsWebGL) return;
            
            #if UNITY_WEBGL && !UNITY_EDITOR
            HideLoadingScreen();
            #endif
        }
        
        public void OptimizeForMobile()
        {
            if (!IsMobileBrowser) return;
            
            // Mobile-specific optimizations
            QualitySettings.SetQualityLevel(1); // Low quality for mobile
            Application.targetFrameRate = 30; // Lower frame rate for mobile
            
            // Reduce texture quality further
            QualitySettings.masterTextureLimit = 2; // Quarter resolution
            
            // Disable expensive effects
            QualitySettings.shadows = ShadowQuality.Disable;
            QualitySettings.softParticles = false;
            
            Debug.Log("Mobile optimizations applied");
        }
        
        public void HandleWebGLMemoryWarning()
        {
            // Handle low memory situations in WebGL
            Debug.LogWarning("WebGL memory warning - applying emergency optimizations");
            
            // Force garbage collection
            System.GC.Collect();
            
            // Reduce quality further
            QualitySettings.SetQualityLevel(0); // Fastest quality
            QualitySettings.masterTextureLimit = 3; // Eighth resolution
            
            // Disable non-essential features
            var audioListener = FindObjectOfType<AudioListener>();
            if (audioListener != null)
            {
                audioListener.enabled = false;
            }
        }
        
        public Dictionary<string, object> GetWebBuildInfo()
        {
            return new Dictionary<string, object>
            {
                { "isWebGL", IsWebGL },
                { "browserName", BrowserName },
                { "browserVersion", BrowserVersion },
                { "isMobile", IsMobileBrowser },
                { "webXREnabled", enableWebXR },
                { "targetFrameRate", targetFrameRate },
                { "qualityLevel", QualitySettings.GetQualityLevel() }
            };
        }
        
        private void Update()
        {
            // Monitor performance in web builds
            if (IsWebGL && Time.frameCount % 300 == 0) // Check every 5 seconds at 60fps
            {
                MonitorWebPerformance();
            }
        }
        
        private void MonitorWebPerformance()
        {
            float fps = 1f / Time.deltaTime;
            
            // If FPS is too low, apply optimizations
            if (fps < targetFrameRate * 0.7f) // 70% of target FPS
            {
                Debug.LogWarning($"Low FPS detected: {fps:F1}. Applying optimizations...");
                
                // Gradually reduce quality
                int currentQuality = QualitySettings.GetQualityLevel();
                if (currentQuality > 0)
                {
                    QualitySettings.SetQualityLevel(currentQuality - 1);
                }
            }
        }
        
        private void OnApplicationFocus(bool hasFocus)
        {
            if (IsWebGL)
            {
                // Handle browser tab focus changes
                if (hasFocus)
                {
                    Debug.Log("Browser tab gained focus");
                    Time.timeScale = 1f;
                }
                else
                {
                    Debug.Log("Browser tab lost focus");
                    // Optionally pause the game
                    // Time.timeScale = 0f;
                }
            }
        }
        
        private void OnApplicationPause(bool pauseStatus)
        {
            if (IsWebGL)
            {
                // Handle browser pause/resume
                if (pauseStatus)
                {
                    Debug.Log("Application paused");
                }
                else
                {
                    Debug.Log("Application resumed");
                }
            }
        }
    }
}

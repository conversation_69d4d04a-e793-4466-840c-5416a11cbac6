using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Runtime.InteropServices;

namespace ARCarRacing.Multiplayer
{
    /// <summary>
    /// Web-optimized multiplayer manager using WebRTC or WebSocket
    /// </summary>
    public class WebMultiplayerManager : MonoBehaviour
    {
        [Header("Web Multiplayer Settings")]
        [SerializeField] private string serverUrl = "wss://your-websocket-server.com";
        [SerializeField] private int maxPlayersPerRoom = 4;
        [SerializeField] private float syncInterval = 0.1f;
        [SerializeField] private bool useWebRTC = false;
        
        [Header("Fallback Settings")]
        [SerializeField] private bool enableOfflineMode = true;
        [SerializeField] private bool simulateMultiplayer = false;
        
        // Connection state
        public bool IsConnected { get; private set; } = false;
        public bool IsInRoom { get; private set; } = false;
        public string RoomId { get; private set; } = "";
        public string PlayerId { get; private set; } = "";
        public int PlayerCount { get; private set; } = 1;
        
        // Events
        public System.Action OnConnected;
        public System.Action OnDisconnected;
        public System.Action<string> OnJoinedRoom;
        public System.Action OnLeftRoom;
        public System.Action<WebPlayerData> OnPlayerJoined;
        public System.Action<string> OnPlayerLeft;
        public System.Action<string> OnConnectionError;
        
        // Player data
        private Dictionary<string, WebPlayerData> players = new Dictionary<string, WebPlayerData>();
        private WebPlayerData localPlayer;
        
        // Singleton instance
        public static WebMultiplayerManager Instance { get; private set; }
        
        // JavaScript interface for WebSocket/WebRTC
        #if UNITY_WEBGL && !UNITY_EDITOR
        [DllImport("__Internal")]
        private static extern void ConnectToServer(string url);
        
        [DllImport("__Internal")]
        private static extern void DisconnectFromServer();
        
        [DllImport("__Internal")]
        private static extern void CreateRoom(string roomName);
        
        [DllImport("__Internal")]
        private static extern void JoinRoom(string roomId);
        
        [DllImport("__Internal")]
        private static extern void LeaveRoom();
        
        [DllImport("__Internal")]
        private static extern void SendPlayerData(string data);
        
        [DllImport("__Internal")]
        private static extern void SendChatMessage(string message);
        #else
        private static void ConnectToServer(string url) { }
        private static void DisconnectFromServer() { }
        private static void CreateRoom(string roomName) { }
        private static void JoinRoom(string roomId) { }
        private static void LeaveRoom() { }
        private static void SendPlayerData(string data) { }
        private static void SendChatMessage(string message) { }
        #endif
        
        [System.Serializable]
        public class WebPlayerData
        {
            public string playerId;
            public string playerName;
            public Vector3 position;
            public Quaternion rotation;
            public Vector3 velocity;
            public float speed;
            public bool isReady;
            public int score;
            public float timestamp;
            
            public WebPlayerData()
            {
                playerId = System.Guid.NewGuid().ToString();
                playerName = "Player";
                position = Vector3.zero;
                rotation = Quaternion.identity;
                velocity = Vector3.zero;
                speed = 0f;
                isReady = false;
                score = 0;
                timestamp = Time.time;
            }
            
            public string ToJson()
            {
                return JsonUtility.ToJson(this);
            }
            
            public static WebPlayerData FromJson(string json)
            {
                return JsonUtility.FromJson<WebPlayerData>(json);
            }
        }
        
        private void Awake()
        {
            // Singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeWebMultiplayer();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void InitializeWebMultiplayer()
        {
            // Generate unique player ID
            PlayerId = System.Guid.NewGuid().ToString();
            
            // Initialize local player data
            localPlayer = new WebPlayerData();
            localPlayer.playerId = PlayerId;
            localPlayer.playerName = "Player_" + Random.Range(1000, 9999);
            
            Debug.Log($"Web multiplayer initialized. Player ID: {PlayerId}");
        }
        
        private void Start()
        {
            // Auto-connect if not in offline mode
            if (!enableOfflineMode)
            {
                StartCoroutine(AutoConnect());
            }
        }
        
        private IEnumerator AutoConnect()
        {
            yield return new WaitForSeconds(1f);
            ConnectToMultiplayerServer();
        }
        
        public void ConnectToMultiplayerServer()
        {
            if (IsConnected) return;
            
            #if UNITY_WEBGL && !UNITY_EDITOR
            ConnectToServer(serverUrl);
            #else
            // Simulate connection in editor
            StartCoroutine(SimulateConnection());
            #endif
            
            Debug.Log($"Connecting to multiplayer server: {serverUrl}");
        }
        
        private IEnumerator SimulateConnection()
        {
            yield return new WaitForSeconds(1f);
            OnServerConnected();
        }
        
        public void DisconnectFromMultiplayerServer()
        {
            if (!IsConnected) return;
            
            #if UNITY_WEBGL && !UNITY_EDITOR
            DisconnectFromServer();
            #endif
            
            IsConnected = false;
            OnDisconnected?.Invoke();
            
            Debug.Log("Disconnected from multiplayer server");
        }
        
        public void CreateMultiplayerRoom(string roomName = "")
        {
            if (!IsConnected)
            {
                Debug.LogWarning("Not connected to server");
                return;
            }
            
            if (string.IsNullOrEmpty(roomName))
            {
                roomName = "Room_" + Random.Range(1000, 9999);
            }
            
            #if UNITY_WEBGL && !UNITY_EDITOR
            CreateRoom(roomName);
            #else
            // Simulate room creation
            StartCoroutine(SimulateRoomCreation(roomName));
            #endif
            
            Debug.Log($"Creating room: {roomName}");
        }
        
        private IEnumerator SimulateRoomCreation(string roomName)
        {
            yield return new WaitForSeconds(0.5f);
            OnRoomCreated(roomName);
        }
        
        public void JoinMultiplayerRoom(string roomId)
        {
            if (!IsConnected)
            {
                Debug.LogWarning("Not connected to server");
                return;
            }
            
            #if UNITY_WEBGL && !UNITY_EDITOR
            JoinRoom(roomId);
            #else
            // Simulate joining room
            StartCoroutine(SimulateJoinRoom(roomId));
            #endif
            
            Debug.Log($"Joining room: {roomId}");
        }
        
        private IEnumerator SimulateJoinRoom(string roomId)
        {
            yield return new WaitForSeconds(0.5f);
            OnRoomJoined(roomId);
        }
        
        public void LeaveMultiplayerRoom()
        {
            if (!IsInRoom) return;
            
            #if UNITY_WEBGL && !UNITY_EDITOR
            LeaveRoom();
            #endif
            
            IsInRoom = false;
            RoomId = "";
            players.Clear();
            PlayerCount = 1;
            
            OnLeftRoom?.Invoke();
            
            Debug.Log("Left multiplayer room");
        }
        
        public void UpdatePlayerData(Vector3 position, Quaternion rotation, Vector3 velocity, float speed)
        {
            if (!IsInRoom) return;
            
            localPlayer.position = position;
            localPlayer.rotation = rotation;
            localPlayer.velocity = velocity;
            localPlayer.speed = speed;
            localPlayer.timestamp = Time.time;
            
            // Send to other players
            string playerData = localPlayer.ToJson();
            
            #if UNITY_WEBGL && !UNITY_EDITOR
            SendPlayerData(playerData);
            #endif
        }
        
        public void SendMessage(string message)
        {
            if (!IsInRoom) return;
            
            #if UNITY_WEBGL && !UNITY_EDITOR
            SendChatMessage(message);
            #endif
            
            Debug.Log($"Sent message: {message}");
        }
        
        public List<WebPlayerData> GetOtherPlayers()
        {
            List<WebPlayerData> otherPlayers = new List<WebPlayerData>();
            
            foreach (var player in players.Values)
            {
                if (player.playerId != PlayerId)
                {
                    otherPlayers.Add(player);
                }
            }
            
            return otherPlayers;
        }
        
        public WebPlayerData GetPlayer(string playerId)
        {
            return players.ContainsKey(playerId) ? players[playerId] : null;
        }
        
        public void SetPlayerReady(bool ready)
        {
            localPlayer.isReady = ready;
            
            // Send updated data
            if (IsInRoom)
            {
                string playerData = localPlayer.ToJson();
                
                #if UNITY_WEBGL && !UNITY_EDITOR
                SendPlayerData(playerData);
                #endif
            }
        }
        
        // JavaScript callback methods
        public void OnServerConnected()
        {
            IsConnected = true;
            OnConnected?.Invoke();
            Debug.Log("Connected to multiplayer server");
        }
        
        public void OnServerDisconnected()
        {
            IsConnected = false;
            IsInRoom = false;
            OnDisconnected?.Invoke();
            Debug.Log("Disconnected from multiplayer server");
        }
        
        public void OnRoomCreated(string roomId)
        {
            RoomId = roomId;
            IsInRoom = true;
            PlayerCount = 1;
            players.Clear();
            players[PlayerId] = localPlayer;
            
            OnJoinedRoom?.Invoke(roomId);
            Debug.Log($"Room created: {roomId}");
        }
        
        public void OnRoomJoined(string roomId)
        {
            RoomId = roomId;
            IsInRoom = true;
            
            if (!players.ContainsKey(PlayerId))
            {
                players[PlayerId] = localPlayer;
            }
            
            OnJoinedRoom?.Invoke(roomId);
            Debug.Log($"Joined room: {roomId}");
        }
        
        public void OnPlayerDataReceived(string playerDataJson)
        {
            try
            {
                WebPlayerData playerData = WebPlayerData.FromJson(playerDataJson);
                
                if (playerData.playerId != PlayerId)
                {
                    bool isNewPlayer = !players.ContainsKey(playerData.playerId);
                    players[playerData.playerId] = playerData;
                    
                    if (isNewPlayer)
                    {
                        PlayerCount = players.Count;
                        OnPlayerJoined?.Invoke(playerData);
                        Debug.Log($"Player joined: {playerData.playerName}");
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error parsing player data: {e.Message}");
            }
        }
        
        public void OnPlayerDisconnected(string playerId)
        {
            if (players.ContainsKey(playerId))
            {
                players.Remove(playerId);
                PlayerCount = players.Count;
                OnPlayerLeft?.Invoke(playerId);
                Debug.Log($"Player left: {playerId}");
            }
        }
        
        public void OnMultiplayerError(string error)
        {
            Debug.LogError($"Multiplayer error: {error}");
            OnConnectionError?.Invoke(error);
        }
        
        private void Update()
        {
            // Sync player data at regular intervals
            if (IsInRoom && Time.time % syncInterval < Time.deltaTime)
            {
                SyncLocalPlayerData();
            }
        }
        
        private void SyncLocalPlayerData()
        {
            // Get local player's car data
            var carController = FindObjectOfType<Car.CarController>();
            if (carController != null)
            {
                UpdatePlayerData(
                    carController.transform.position,
                    carController.transform.rotation,
                    carController.Velocity,
                    carController.CurrentSpeed
                );
            }
        }
        
        private void OnDestroy()
        {
            if (IsConnected)
            {
                DisconnectFromMultiplayerServer();
            }
        }
    }
}

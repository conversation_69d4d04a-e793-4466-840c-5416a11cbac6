using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.XR.ARFoundation;
using UnityEngine.XR.ARSubsystems;
using UnityEngine.EventSystems;

namespace ARCarRacing.AR
{
    /// <summary>
    /// Handles AR object placement with touch input
    /// </summary>
    public class ARPlacementController : MonoBehaviour
    {
        [Header("Placement Settings")]
        [SerializeField] private GameObject placementIndicator;
        [SerializeField] private LayerMask placementLayerMask = 1;
        [SerializeField] private float placementDistance = 2f;
        [SerializeField] private bool showPlacementIndicator = true;
        
        [Header("Touch Settings")]
        [SerializeField] private float touchSensitivity = 1f;
        [SerializeField] private float holdTimeForPlacement = 0.5f;
        [SerializeField] private bool requireTapToPlace = true;
        
        // Components
        private ARRaycastManager raycastManager;
        private ARPlaneManager planeManager;
        private Camera arCamera;
        
        // Placement state
        public bool IsPlacementMode { get; private set; } = false;
        public bool CanPlace { get; private set; } = false;
        public Vector3 PlacementPosition { get; private set; }
        public Quaternion PlacementRotation { get; private set; }
        
        // Touch tracking
        private float touchStartTime;
        private bool isTouching = false;
        private Vector2 lastTouchPosition;
        
        // Raycast results
        private List<ARRaycastHit> raycastHits = new List<ARRaycastHit>();
        
        // Events
        public System.Action<Vector3, Quaternion> OnValidPlacementFound;
        public System.Action OnPlacementLost;
        public System.Action<Vector3, Quaternion> OnPlacementConfirmed;
        
        private void Awake()
        {
            // Get required components
            raycastManager = FindObjectOfType<ARRaycastManager>();
            planeManager = FindObjectOfType<ARPlaneManager>();
            arCamera = Camera.main;
            
            if (arCamera == null)
            {
                arCamera = FindObjectOfType<ARCamera>()?.GetComponent<Camera>();
            }
        }
        
        private void Start()
        {
            // Initialize placement indicator
            if (placementIndicator != null)
            {
                placementIndicator.SetActive(false);
            }
        }
        
        private void Update()
        {
            if (!IsPlacementMode) return;
            
            // Handle touch input
            HandleTouchInput();
            
            // Update placement position
            UpdatePlacementPosition();
            
            // Update placement indicator
            UpdatePlacementIndicator();
        }
        
        private void HandleTouchInput()
        {
            // Handle touch input for placement
            if (Input.touchCount > 0)
            {
                Touch touch = Input.GetTouch(0);
                
                // Skip if touching UI
                if (EventSystem.current.IsPointerOverGameObject(touch.fingerId))
                    return;
                
                switch (touch.phase)
                {
                    case TouchPhase.Began:
                        HandleTouchBegan(touch);
                        break;
                        
                    case TouchPhase.Moved:
                    case TouchPhase.Stationary:
                        HandleTouchMoved(touch);
                        break;
                        
                    case TouchPhase.Ended:
                        HandleTouchEnded(touch);
                        break;
                }
            }
            else if (Input.GetMouseButtonDown(0) && Application.isEditor)
            {
                // Handle mouse input in editor
                HandleMouseInput();
            }
        }
        
        private void HandleTouchBegan(Touch touch)
        {
            isTouching = true;
            touchStartTime = Time.time;
            lastTouchPosition = touch.position;
        }
        
        private void HandleTouchMoved(Touch touch)
        {
            lastTouchPosition = touch.position;
        }
        
        private void HandleTouchEnded(Touch touch)
        {
            if (!isTouching) return;
            
            float touchDuration = Time.time - touchStartTime;
            
            if (requireTapToPlace)
            {
                // Quick tap to place
                if (touchDuration < 0.3f && CanPlace)
                {
                    ConfirmPlacement();
                }
            }
            else
            {
                // Hold to place
                if (touchDuration >= holdTimeForPlacement && CanPlace)
                {
                    ConfirmPlacement();
                }
            }
            
            isTouching = false;
        }
        
        private void HandleMouseInput()
        {
            if (CanPlace)
            {
                ConfirmPlacement();
            }
        }
        
        private void UpdatePlacementPosition()
        {
            Vector2 screenPosition;
            
            // Use touch position if touching, otherwise use screen center
            if (isTouching && Input.touchCount > 0)
            {
                screenPosition = lastTouchPosition;
            }
            else
            {
                screenPosition = new Vector2(Screen.width * 0.5f, Screen.height * 0.5f);
            }
            
            // Perform raycast to find placement position
            if (raycastManager.Raycast(screenPosition, raycastHits, TrackableType.PlaneWithinPolygon))
            {
                var hit = raycastHits[0];
                
                PlacementPosition = hit.pose.position;
                PlacementRotation = hit.pose.rotation;
                
                if (!CanPlace)
                {
                    CanPlace = true;
                    OnValidPlacementFound?.Invoke(PlacementPosition, PlacementRotation);
                }
            }
            else
            {
                if (CanPlace)
                {
                    CanPlace = false;
                    OnPlacementLost?.Invoke();
                }
            }
        }
        
        private void UpdatePlacementIndicator()
        {
            if (placementIndicator == null || !showPlacementIndicator) return;
            
            if (CanPlace)
            {
                placementIndicator.SetActive(true);
                placementIndicator.transform.position = PlacementPosition;
                placementIndicator.transform.rotation = PlacementRotation;
                
                // Animate indicator (optional)
                AnimatePlacementIndicator();
            }
            else
            {
                placementIndicator.SetActive(false);
            }
        }
        
        private void AnimatePlacementIndicator()
        {
            // Simple scale animation
            float scale = 1f + Mathf.Sin(Time.time * 3f) * 0.1f;
            placementIndicator.transform.localScale = Vector3.one * scale;
        }
        
        public void EnablePlacementMode(bool enable)
        {
            IsPlacementMode = enable;
            
            if (!enable)
            {
                CanPlace = false;
                
                if (placementIndicator != null)
                {
                    placementIndicator.SetActive(false);
                }
            }
            
            Debug.Log($"Placement mode {(enable ? "enabled" : "disabled")}");
        }
        
        public void ConfirmPlacement()
        {
            if (!CanPlace) return;
            
            OnPlacementConfirmed?.Invoke(PlacementPosition, PlacementRotation);
            
            Debug.Log($"Placement confirmed at position: {PlacementPosition}");
        }
        
        public void SetPlacementIndicator(GameObject indicator)
        {
            if (placementIndicator != null)
            {
                placementIndicator.SetActive(false);
            }
            
            placementIndicator = indicator;
            
            if (placementIndicator != null && IsPlacementMode)
            {
                placementIndicator.SetActive(CanPlace);
            }
        }
        
        public Vector3 GetWorldPositionFromScreenPoint(Vector2 screenPoint)
        {
            if (raycastManager.Raycast(screenPoint, raycastHits, TrackableType.PlaneWithinPolygon))
            {
                return raycastHits[0].pose.position;
            }
            
            return Vector3.zero;
        }
        
        public bool IsValidPlacementPosition(Vector2 screenPoint)
        {
            return raycastManager.Raycast(screenPoint, raycastHits, TrackableType.PlaneWithinPolygon);
        }
        
        public void SetPlacementDistance(float distance)
        {
            placementDistance = Mathf.Clamp(distance, 0.5f, 10f);
        }
        
        public void SetTouchSensitivity(float sensitivity)
        {
            touchSensitivity = Mathf.Clamp(sensitivity, 0.1f, 3f);
        }
        
        private void OnDrawGizmosSelected()
        {
            if (!IsPlacementMode) return;
            
            // Draw placement position
            if (CanPlace)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawWireSphere(PlacementPosition, 0.2f);
                Gizmos.DrawRay(PlacementPosition, PlacementRotation * Vector3.up * 0.5f);
            }
            
            // Draw camera ray
            if (arCamera != null)
            {
                Vector2 screenCenter = new Vector2(Screen.width * 0.5f, Screen.height * 0.5f);
                Ray ray = arCamera.ScreenPointToRay(screenCenter);
                
                Gizmos.color = Color.blue;
                Gizmos.DrawRay(ray.origin, ray.direction * placementDistance);
            }
        }
    }
}

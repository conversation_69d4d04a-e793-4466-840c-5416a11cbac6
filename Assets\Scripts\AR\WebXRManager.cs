using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Runtime.InteropServices;

namespace ARCarRacing.AR
{
    /// <summary>
    /// WebXR manager for browser-based AR functionality
    /// </summary>
    public class WebXRManager : MonoBehaviour
    {
        [Header("WebXR Settings")]
        [SerializeField] private bool enableWebXR = true;
        [SerializeField] private bool fallbackToNonAR = true;
        [SerializeField] private GameObject trackPrefab;
        [SerializeField] private Transform fallbackTrackPosition;
        
        [Header("Web AR UI")]
        [SerializeField] private GameObject webARButton;
        [SerializeField] private GameObject arNotSupportedMessage;
        [SerializeField] private GameObject placementInstructions;
        
        // WebXR state
        public bool IsWebXRSupported { get; private set; } = false;
        public bool IsARSessionActive { get; private set; } = false;
        public bool IsTrackPlaced { get; private set; } = false;
        
        // Events
        public System.Action OnWebXRInitialized;
        public System.Action OnARSessionStarted;
        public System.Action OnARSessionEnded;
        public System.Action<Vector3, Quaternion> OnTrackPlaced;
        public System.Action OnTrackRemoved;
        
        // JavaScript interface
        #if UNITY_WEBGL && !UNITY_EDITOR
        [DllImport("__Internal")]
        private static extern bool IsWebXRSupported();
        
        [DllImport("__Internal")]
        private static extern void StartARSession();
        
        [DllImport("__Internal")]
        private static extern void EndARSession();
        
        [DllImport("__Internal")]
        private static extern bool IsARSessionRunning();
        
        [DllImport("__Internal")]
        private static extern void RequestHitTest(float screenX, float screenY);
        
        [DllImport("__Internal")]
        private static extern void PlaceObject(float x, float y, float z);
        #else
        private static bool IsWebXRSupported() { return false; }
        private static void StartARSession() { }
        private static void EndARSession() { }
        private static bool IsARSessionRunning() { return false; }
        private static void RequestHitTest(float screenX, float screenY) { }
        private static void PlaceObject(float x, float y, float z) { }
        #endif
        
        // Singleton instance
        public static WebXRManager Instance { get; private set; }
        
        // Fallback camera for non-AR mode
        private Camera mainCamera;
        private GameObject placedTrack;
        
        private void Awake()
        {
            // Singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
            }
            
            mainCamera = Camera.main;
        }
        
        private void Start()
        {
            StartCoroutine(InitializeWebXR());
        }
        
        private IEnumerator InitializeWebXR()
        {
            yield return new WaitForSeconds(0.5f); // Wait for page to load
            
            #if UNITY_WEBGL && !UNITY_EDITOR
            IsWebXRSupported = IsWebXRSupported();
            #else
            IsWebXRSupported = false; // Not supported in editor
            #endif
            
            if (IsWebXRSupported)
            {
                Debug.Log("WebXR is supported!");
                SetupWebXRUI();
            }
            else
            {
                Debug.Log("WebXR not supported, using fallback mode");
                SetupFallbackMode();
            }
            
            OnWebXRInitialized?.Invoke();
        }
        
        private void SetupWebXRUI()
        {
            if (webARButton != null)
            {
                webARButton.SetActive(true);
            }
            
            if (arNotSupportedMessage != null)
            {
                arNotSupportedMessage.SetActive(false);
            }
        }
        
        private void SetupFallbackMode()
        {
            if (webARButton != null)
            {
                webARButton.SetActive(false);
            }
            
            if (arNotSupportedMessage != null)
            {
                arNotSupportedMessage.SetActive(true);
            }
            
            if (fallbackToNonAR)
            {
                // Place track in a default position for non-AR play
                StartCoroutine(PlaceFallbackTrack());
            }
        }
        
        private IEnumerator PlaceFallbackTrack()
        {
            yield return new WaitForSeconds(1f);
            
            Vector3 trackPosition = fallbackTrackPosition != null ? 
                fallbackTrackPosition.position : 
                mainCamera.transform.position + mainCamera.transform.forward * 5f;
            
            Quaternion trackRotation = Quaternion.identity;
            
            PlaceTrackAtPosition(trackPosition, trackRotation);
        }
        
        public void StartWebXRSession()
        {
            if (!IsWebXRSupported)
            {
                Debug.LogWarning("WebXR not supported on this device/browser");
                return;
            }
            
            #if UNITY_WEBGL && !UNITY_EDITOR
            StartARSession();
            #endif
            
            IsARSessionActive = true;
            OnARSessionStarted?.Invoke();
            
            if (placementInstructions != null)
            {
                placementInstructions.SetActive(true);
            }
            
            Debug.Log("WebXR AR session started");
        }
        
        public void EndWebXRSession()
        {
            if (!IsARSessionActive) return;
            
            #if UNITY_WEBGL && !UNITY_EDITOR
            EndARSession();
            #endif
            
            IsARSessionActive = false;
            OnARSessionEnded?.Invoke();
            
            if (placementInstructions != null)
            {
                placementInstructions.SetActive(false);
            }
            
            Debug.Log("WebXR AR session ended");
        }
        
        private void Update()
        {
            // Handle input for track placement
            if (IsARSessionActive || (!IsWebXRSupported && fallbackToNonAR))
            {
                HandleInput();
            }
        }
        
        private void HandleInput()
        {
            // Handle mouse/touch input for track placement
            if (Input.GetMouseButtonDown(0))
            {
                Vector2 screenPosition = Input.mousePosition;
                
                if (IsARSessionActive)
                {
                    // Request hit test from WebXR
                    #if UNITY_WEBGL && !UNITY_EDITOR
                    RequestHitTest(screenPosition.x, screenPosition.y);
                    #endif
                }
                else if (!IsTrackPlaced)
                {
                    // Fallback mode - place track in front of camera
                    PlaceFallbackTrackAtClick(screenPosition);
                }
            }
        }
        
        private void PlaceFallbackTrackAtClick(Vector2 screenPosition)
        {
            Ray ray = mainCamera.ScreenPointToRay(screenPosition);
            
            // Cast ray to find placement position
            if (Physics.Raycast(ray, out RaycastHit hit, 20f))
            {
                PlaceTrackAtPosition(hit.point, Quaternion.identity);
            }
            else
            {
                // Place at fixed distance if no hit
                Vector3 position = ray.origin + ray.direction * 5f;
                position.y = 0; // Place on ground level
                PlaceTrackAtPosition(position, Quaternion.identity);
            }
        }
        
        // Called from JavaScript when hit test result is available
        public void OnHitTestResult(string positionData)
        {
            try
            {
                string[] coords = positionData.Split(',');
                if (coords.Length >= 3)
                {
                    float x = float.Parse(coords[0]);
                    float y = float.Parse(coords[1]);
                    float z = float.Parse(coords[2]);
                    
                    Vector3 position = new Vector3(x, y, z);
                    Quaternion rotation = Quaternion.identity;
                    
                    PlaceTrackAtPosition(position, rotation);
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error parsing hit test result: {e.Message}");
            }
        }
        
        private void PlaceTrackAtPosition(Vector3 position, Quaternion rotation)
        {
            if (IsTrackPlaced)
            {
                RemoveTrack();
            }
            
            if (trackPrefab != null)
            {
                placedTrack = Instantiate(trackPrefab, position, rotation);
                IsTrackPlaced = true;
                
                OnTrackPlaced?.Invoke(position, rotation);
                
                if (placementInstructions != null)
                {
                    placementInstructions.SetActive(false);
                }
                
                Debug.Log($"Track placed at position: {position}");
            }
        }
        
        public void RemoveTrack()
        {
            if (placedTrack != null)
            {
                Destroy(placedTrack);
                placedTrack = null;
            }
            
            IsTrackPlaced = false;
            OnTrackRemoved?.Invoke();
            
            if (IsARSessionActive && placementInstructions != null)
            {
                placementInstructions.SetActive(true);
            }
            
            Debug.Log("Track removed");
        }
        
        public Vector3 GetTrackPosition()
        {
            return placedTrack != null ? placedTrack.transform.position : Vector3.zero;
        }
        
        public Quaternion GetTrackRotation()
        {
            return placedTrack != null ? placedTrack.transform.rotation : Quaternion.identity;
        }
        
        // Web-specific utility methods
        public bool IsRunningInBrowser()
        {
            #if UNITY_WEBGL && !UNITY_EDITOR
            return true;
            #else
            return false;
            #endif
        }
        
        public void ShowWebXRInstructions()
        {
            string instructions = IsWebXRSupported ? 
                "Tap 'Start AR' to begin AR session, then tap on surfaces to place the track." :
                "AR not supported. Click to place track in 3D space.";
            
            Debug.Log(instructions);
            
            // You could show this in UI
            var uiManager = FindObjectOfType<UI.UIManager>();
            if (uiManager != null)
            {
                uiManager.ShowMessage(instructions, 5f);
            }
        }
        
        private void OnDestroy()
        {
            if (IsARSessionActive)
            {
                EndWebXRSession();
            }
        }
        
        // JavaScript callback methods (called from browser)
        public void OnWebXRSessionStarted()
        {
            IsARSessionActive = true;
            OnARSessionStarted?.Invoke();
        }
        
        public void OnWebXRSessionEnded()
        {
            IsARSessionActive = false;
            OnARSessionEnded?.Invoke();
        }
        
        public void OnWebXRError(string error)
        {
            Debug.LogError($"WebXR Error: {error}");
            
            if (fallbackToNonAR)
            {
                SetupFallbackMode();
            }
        }
    }
}

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using ARCarRacing.Car;
using ARCarRacing.Managers;

namespace ARCarRacing.GameModes
{
    /// <summary>
    /// Base class for all game modes
    /// </summary>
    public abstract class GameModeBase : MonoBehaviour
    {
        [Header("Game Mode Settings")]
        [SerializeField] protected string gameModeName = "Base Game Mode";
        [SerializeField] protected string gameModeDescription = "Base game mode description";
        [SerializeField] protected float gameDuration = 300f; // 5 minutes default
        [SerializeField] protected int maxPlayers = 4;
        [SerializeField] protected bool allowSpectators = true;
        
        [Header("Scoring")]
        [SerializeField] protected bool useScoring = true;
        [SerializeField] protected int[] positionPoints = { 100, 75, 50, 25 }; // Points for 1st, 2nd, 3rd, 4th place
        
        // Game state
        public bool IsGameActive { get; protected set; } = false;
        public bool IsGameFinished { get; protected set; } = false;
        public float GameTime { get; protected set; } = 0f;
        public float RemainingTime => Mathf.Max(0f, gameDuration - GameTime);
        
        // Players
        protected List<CarController> players = new List<CarController>();
        protected Dictionary<CarController, PlayerGameData> playerData = new Dictionary<CarController, PlayerGameData>();
        
        // Events
        public System.Action OnGameModeStarted;
        public System.Action OnGameModeFinished;
        public System.Action<CarController> OnPlayerJoined;
        public System.Action<CarController> OnPlayerLeft;
        public System.Action<CarController, int> OnPlayerScored;
        public System.Action<List<PlayerGameData>> OnLeaderboardUpdated;
        
        // Player data structure
        [System.Serializable]
        public class PlayerGameData
        {
            public CarController player;
            public string playerName;
            public int score;
            public int position;
            public float bestLapTime;
            public int lapsCompleted;
            public bool isFinished;
            public float finishTime;
            
            public PlayerGameData(CarController car, string name)
            {
                player = car;
                playerName = name;
                score = 0;
                position = 0;
                bestLapTime = float.MaxValue;
                lapsCompleted = 0;
                isFinished = false;
                finishTime = 0f;
            }
        }
        
        protected virtual void Awake()
        {
            // Subscribe to game manager events
            if (GameManager.Instance != null)
            {
                GameManager.Instance.OnGameStarted += OnGameStarted;
                GameManager.Instance.OnGameEnded += OnGameEnded;
            }
        }
        
        protected virtual void Update()
        {
            if (!IsGameActive || IsGameFinished) return;
            
            // Update game time
            GameTime += Time.deltaTime;
            
            // Check for game end conditions
            CheckGameEndConditions();
            
            // Update game mode specific logic
            UpdateGameMode();
        }
        
        protected virtual void OnGameStarted()
        {
            StartGameMode();
        }
        
        protected virtual void OnGameEnded()
        {
            EndGameMode();
        }
        
        public virtual void StartGameMode()
        {
            IsGameActive = true;
            IsGameFinished = false;
            GameTime = 0f;
            
            // Initialize all players
            foreach (var player in players)
            {
                InitializePlayer(player);
            }
            
            OnGameModeStarted?.Invoke();
            
            Debug.Log($"Game mode '{gameModeName}' started with {players.Count} players");
        }
        
        public virtual void EndGameMode()
        {
            IsGameActive = false;
            IsGameFinished = true;
            
            // Calculate final results
            CalculateFinalResults();
            
            OnGameModeFinished?.Invoke();
            
            Debug.Log($"Game mode '{gameModeName}' finished");
        }
        
        public virtual void AddPlayer(CarController player, string playerName = "")
        {
            if (players.Contains(player)) return;
            
            if (players.Count >= maxPlayers)
            {
                Debug.LogWarning($"Cannot add player - maximum players ({maxPlayers}) reached");
                return;
            }
            
            players.Add(player);
            
            if (string.IsNullOrEmpty(playerName))
            {
                playerName = $"Player {players.Count}";
            }
            
            PlayerGameData data = new PlayerGameData(player, playerName);
            playerData[player] = data;
            
            if (IsGameActive)
            {
                InitializePlayer(player);
            }
            
            OnPlayerJoined?.Invoke(player);
            
            Debug.Log($"Player '{playerName}' joined the game");
        }
        
        public virtual void RemovePlayer(CarController player)
        {
            if (!players.Contains(player)) return;
            
            players.Remove(player);
            
            if (playerData.ContainsKey(player))
            {
                playerData.Remove(player);
            }
            
            OnPlayerLeft?.Invoke(player);
            
            Debug.Log($"Player removed from game");
        }
        
        protected virtual void InitializePlayer(CarController player)
        {
            if (player == null) return;
            
            // Reset player car
            player.ResetCar();
            player.SetCarEnabled(true);
            
            // Position player at start
            PositionPlayerAtStart(player);
        }
        
        protected virtual void PositionPlayerAtStart(CarController player)
        {
            // Override in derived classes to position players appropriately
            // Default implementation does nothing
        }
        
        protected virtual void CheckGameEndConditions()
        {
            // Check time limit
            if (gameDuration > 0 && GameTime >= gameDuration)
            {
                EndGameMode();
                return;
            }
            
            // Override in derived classes for specific end conditions
        }
        
        protected virtual void UpdateGameMode()
        {
            // Override in derived classes for game mode specific updates
        }
        
        protected virtual void CalculateFinalResults()
        {
            // Sort players by their performance
            List<PlayerGameData> sortedPlayers = new List<PlayerGameData>(playerData.Values);
            SortPlayersByPerformance(sortedPlayers);
            
            // Assign final positions and scores
            for (int i = 0; i < sortedPlayers.Count; i++)
            {
                sortedPlayers[i].position = i + 1;
                
                if (useScoring && i < positionPoints.Length)
                {
                    sortedPlayers[i].score += positionPoints[i];
                }
            }
            
            OnLeaderboardUpdated?.Invoke(sortedPlayers);
        }
        
        protected virtual void SortPlayersByPerformance(List<PlayerGameData> players)
        {
            // Default sorting by laps completed, then by best lap time
            players.Sort((a, b) =>
            {
                if (a.lapsCompleted != b.lapsCompleted)
                    return b.lapsCompleted.CompareTo(a.lapsCompleted);
                
                return a.bestLapTime.CompareTo(b.bestLapTime);
            });
        }
        
        public virtual void UpdatePlayerScore(CarController player, int points)
        {
            if (playerData.ContainsKey(player))
            {
                playerData[player].score += points;
                OnPlayerScored?.Invoke(player, points);
            }
        }
        
        public virtual void UpdatePlayerLapTime(CarController player, float lapTime)
        {
            if (playerData.ContainsKey(player))
            {
                PlayerGameData data = playerData[player];
                data.lapsCompleted++;
                
                if (lapTime < data.bestLapTime)
                {
                    data.bestLapTime = lapTime;
                }
            }
        }
        
        public virtual PlayerGameData GetPlayerData(CarController player)
        {
            return playerData.ContainsKey(player) ? playerData[player] : null;
        }
        
        public virtual List<PlayerGameData> GetLeaderboard()
        {
            List<PlayerGameData> leaderboard = new List<PlayerGameData>(playerData.Values);
            SortPlayersByPerformance(leaderboard);
            
            // Update positions
            for (int i = 0; i < leaderboard.Count; i++)
            {
                leaderboard[i].position = i + 1;
            }
            
            return leaderboard;
        }
        
        public virtual string GetGameModeInfo()
        {
            return $"{gameModeName}\n{gameModeDescription}\nMax Players: {maxPlayers}\nDuration: {gameDuration}s";
        }
        
        protected virtual void OnDestroy()
        {
            // Unsubscribe from events
            if (GameManager.Instance != null)
            {
                GameManager.Instance.OnGameStarted -= OnGameStarted;
                GameManager.Instance.OnGameEnded -= OnGameEnded;
            }
        }
    }
}

@echo off
echo 🏎️ AR Car Racing - Build and Deploy Script
echo ==========================================

:: Check if Unity is available
where unity >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Unity not found in PATH. Please install Unity or add it to PATH.
    echo    Typical Unity path: C:\Program Files\Unity\Hub\Editor\2022.3.x\Editor\Unity.exe
    pause
    exit /b 1
)

:: Check if Node.js is available
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js not found. Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Prerequisites check passed!
echo.

:: Get current directory
set PROJECT_DIR=%cd%
set BUILD_DIR=%PROJECT_DIR%\Build
set SERVER_DIR=%PROJECT_DIR%\server

echo 📁 Project Directory: %PROJECT_DIR%
echo 📁 Build Directory: %BUILD_DIR%
echo 📁 Server Directory: %SERVER_DIR%
echo.

:: Ask user what they want to do
echo What would you like to do?
echo 1. Build Unity WebGL project only
echo 2. Setup and start local server only
echo 3. Build Unity project and start local server
echo 4. Deploy to production (requires additional setup)
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto build_unity
if "%choice%"=="2" goto setup_server
if "%choice%"=="3" goto build_and_serve
if "%choice%"=="4" goto deploy_production

echo ❌ Invalid choice. Please run the script again.
pause
exit /b 1

:build_unity
echo.
echo 🔨 Building Unity WebGL project...
echo ================================

:: Create build directory if it doesn't exist
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"

:: Unity build command (you may need to adjust the path)
echo 🎮 Starting Unity build process...
echo    This may take 10-30 minutes depending on your project size...

:: Note: You'll need to replace this with the actual Unity build command
:: For now, we'll show instructions
echo.
echo 📋 MANUAL UNITY BUILD REQUIRED:
echo    1. Open Unity Hub
echo    2. Open this project: %PROJECT_DIR%
echo    3. Go to File → Build Settings
echo    4. Select WebGL platform
echo    5. Click "Build" and choose: %BUILD_DIR%
echo.
echo ⏳ Please complete the Unity build manually, then press any key to continue...
pause

:: Check if build was successful
if not exist "%BUILD_DIR%\index.html" (
    echo ❌ Unity build not found. Please ensure the build completed successfully.
    pause
    exit /b 1
)

echo ✅ Unity build completed successfully!
goto end

:setup_server
echo.
echo 🌐 Setting up local development server...
echo =======================================

:: Navigate to server directory
cd /d "%SERVER_DIR%"

:: Check if package.json exists
if not exist "package.json" (
    echo ❌ Server package.json not found. Please ensure the server files are in place.
    pause
    exit /b 1
)

:: Install dependencies
echo 📦 Installing server dependencies...
call npm install

if %errorlevel% neq 0 (
    echo ❌ Failed to install server dependencies.
    pause
    exit /b 1
)

echo ✅ Server dependencies installed successfully!

:: Start the server
echo 🚀 Starting development server...
echo    Game will be available at: http://localhost:8080
echo    WebSocket server at: ws://localhost:8081
echo.
echo 💡 Press Ctrl+C to stop the server
echo.

call npm run dev

goto end

:build_and_serve
echo.
echo 🔨🌐 Building Unity project and starting server...
echo ===============================================

:: First build Unity (manual step)
echo 📋 STEP 1: MANUAL UNITY BUILD REQUIRED
echo    1. Open Unity Hub
echo    2. Open this project: %PROJECT_DIR%
echo    3. Go to File → Build Settings
echo    4. Select WebGL platform
echo    5. Click "Build" and choose: %BUILD_DIR%
echo.
echo ⏳ Please complete the Unity build, then press any key to continue...
pause

:: Check if build was successful
if not exist "%BUILD_DIR%\index.html" (
    echo ❌ Unity build not found. Please ensure the build completed successfully.
    pause
    exit /b 1
)

echo ✅ Unity build found!

:: Setup and start server
cd /d "%SERVER_DIR%"

echo 📦 Installing server dependencies...
call npm install

if %errorlevel% neq 0 (
    echo ❌ Failed to install server dependencies.
    pause
    exit /b 1
)

echo 🚀 Starting development server...
echo    Game will be available at: http://localhost:8080
echo    WebSocket server at: ws://localhost:8081
echo.
echo 💡 Press Ctrl+C to stop the server
echo.

call npm run dev

goto end

:deploy_production
echo.
echo 🚀 Production Deployment Setup...
echo ===============================

echo 📋 Production deployment requires additional setup:
echo.
echo 1. 🏗️  UNITY BUILD:
echo    - Complete WebGL build as described above
echo    - Optimize settings for production
echo.
echo 2. 🌐 WEB HOSTING:
echo    - Deploy Build/ folder to your web host
echo    - Ensure HTTPS is enabled (required for WebXR)
echo    - Configure proper MIME types for .wasm files
echo.
echo 3. 🔗 MULTIPLAYER SERVER:
echo    - Deploy server/ folder to a Node.js hosting service
echo    - Set environment variables (PORT, WS_PORT)
echo    - Update WebSocket URL in Unity scripts
echo.
echo 4. 📱 TESTING:
echo    - Test on mobile devices with AR support
echo    - Verify WebXR functionality
echo    - Test multiplayer connections
echo.
echo 📖 For detailed instructions, see: deploy.md
echo.

set /p deploy_choice="Do you want to start local development server for testing? (y/n): "
if /i "%deploy_choice%"=="y" goto setup_server

goto end

:end
echo.
echo 🎉 Script completed!
echo.
echo 📚 Next Steps:
echo    - For local development: http://localhost:8080
echo    - For production deployment: See deploy.md
echo    - For troubleshooting: Check console logs
echo.
echo 🆘 Need help?
echo    - Check Unity console for build errors
echo    - Verify Node.js server logs
echo    - Test on different browsers/devices
echo.
pause

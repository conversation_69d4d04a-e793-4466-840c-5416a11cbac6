# 🚀 AR Car Racing - Web Deployment Guide

This guide will help you deploy your AR Car Racing game to the web with full WebXR and multiplayer support.

## 📋 Prerequisites

### Required Software:
- **Unity 2022.3 LTS** with WebGL build support
- **Node.js 14+** for the multiplayer server
- **Web server** with HTTPS support (required for WebXR)

### Required Accounts:
- **Hosting provider** (Netlify, Vercel, AWS, etc.)
- **Domain name** (optional but recommended)

## 🏗️ Building the Unity WebGL Project

### Step 1: Configure Unity for WebGL

1. **Open Unity** and load your AR Car Racing project
2. **File → Build Settings**
3. **Switch Platform** to WebGL
4. **Player Settings** configuration:
   ```
   Company Name: Your Company
   Product Name: AR Car Racing
   WebGL Template: ARCarRacing (custom template)
   
   Publishing Settings:
   ✅ Compression Format: Gzip
   ✅ Name Files As Hashes: true
   ✅ Data Caching: true
   
   XR Settings:
   ✅ Initialize XR on Startup: false (we handle this manually)
   ```

### Step 2: Optimize for Web Performance

1. **Quality Settings**:
   - Set default quality to "Medium" or "Low"
   - Disable shadows or set to low quality
   - Reduce texture quality for web

2. **Player Settings → Publishing**:
   ```
   Memory Size: 512 MB (or higher if needed)
   Enable Exceptions: None (for smaller build size)
   Code Optimization: Size
   ```

### Step 3: Build the Project

1. **Build Settings → Build**
2. **Choose output folder**: `Build/`
3. **Wait for build** to complete (may take 10-30 minutes)

## 🌐 Setting Up the Web Server

### Option A: Local Development Server

```bash
# Navigate to your project directory
cd /path/to/your/ar-car-racing-project

# Install server dependencies
cd server
npm install

# Start the development server
npm run dev
```

The game will be available at:
- **Game**: http://localhost:8080
- **WebSocket**: ws://localhost:8081

### Option B: Production Deployment

#### Using Netlify (Recommended for Static Hosting)

1. **Build your Unity project** to the `Build/` folder
2. **Create `netlify.toml`** in your project root:
   ```toml
   [build]
     publish = "Build"
   
   [[headers]]
     for = "/*"
     [headers.values]
       Cross-Origin-Embedder-Policy = "require-corp"
       Cross-Origin-Opener-Policy = "same-origin"
   
   [[headers]]
     for = "*.wasm"
     [headers.values]
       Content-Type = "application/wasm"
   
   [[headers]]
     for = "*.data"
     [headers.values]
       Content-Type = "application/octet-stream"
   ```

3. **Deploy to Netlify**:
   - Connect your GitHub repository
   - Set build command: (leave empty, we build locally)
   - Set publish directory: `Build`
   - Deploy!

#### Using Vercel

1. **Install Vercel CLI**:
   ```bash
   npm install -g vercel
   ```

2. **Create `vercel.json`**:
   ```json
   {
     "builds": [
       {
         "src": "Build/**",
         "use": "@vercel/static"
       }
     ],
     "routes": [
       {
         "src": "/(.*)",
         "dest": "/Build/$1"
       }
     ],
     "headers": [
       {
         "source": "/(.*)",
         "headers": [
           {
             "key": "Cross-Origin-Embedder-Policy",
             "value": "require-corp"
           },
           {
             "key": "Cross-Origin-Opener-Policy",
             "value": "same-origin"
           }
         ]
       }
     ]
   }
   ```

3. **Deploy**:
   ```bash
   vercel --prod
   ```

## 🔗 Multiplayer Server Deployment

### Option A: Heroku (Simple)

1. **Create `Procfile`**:
   ```
   web: node server/server.js
   ```

2. **Update `server/package.json`**:
   ```json
   {
     "scripts": {
       "start": "node server.js"
     }
   }
   ```

3. **Deploy to Heroku**:
   ```bash
   heroku create your-ar-racing-server
   git add .
   git commit -m "Deploy to Heroku"
   git push heroku main
   ```

### Option B: Railway (Modern Alternative)

1. **Connect your GitHub repo** to Railway
2. **Set environment variables**:
   ```
   PORT=8080
   WS_PORT=8081
   NODE_ENV=production
   ```
3. **Deploy automatically** on push

### Option C: DigitalOcean App Platform

1. **Create new app** from GitHub
2. **Configure build settings**:
   ```yaml
   name: ar-racing-server
   services:
   - name: server
     source_dir: /server
     github:
       repo: your-username/ar-car-racing
       branch: main
     run_command: node server.js
     environment_slug: node-js
     instance_count: 1
     instance_size_slug: basic-xxs
   ```

## 🔒 HTTPS Setup (Required for WebXR)

WebXR requires HTTPS. Most hosting providers offer free SSL certificates:

### Netlify/Vercel:
- Automatic HTTPS with free SSL certificates
- Custom domains supported

### Heroku:
- Free SSL for `.herokuapp.com` domains
- Custom domain SSL available

### Cloudflare (Recommended):
1. **Add your domain** to Cloudflare
2. **Enable "Always Use HTTPS"**
3. **Set SSL/TLS mode** to "Full"

## 🧪 Testing Your Deployment

### WebXR Testing:
1. **Open your deployed game** on a mobile device
2. **Use Chrome or Edge** (best WebXR support)
3. **Test AR functionality**:
   - Should prompt for camera permission
   - Should detect planes on flat surfaces
   - Should allow track placement

### Multiplayer Testing:
1. **Open game in multiple browser tabs**
2. **Create a room** in one tab
3. **Join the room** from another tab
4. **Test real-time synchronization**

### Performance Testing:
- **Mobile devices**: Test on various Android/iOS devices
- **Different browsers**: Chrome, Safari, Firefox, Edge
- **Network conditions**: Test on slow connections

## 🔧 Troubleshooting

### Common Issues:

#### "WebXR not supported"
- **Solution**: Ensure HTTPS is enabled
- **Check**: Browser compatibility (Chrome/Edge recommended)

#### "Failed to load WebAssembly"
- **Solution**: Configure proper MIME types on server
- **Check**: `.wasm` files are served with correct headers

#### "Multiplayer connection failed"
- **Solution**: Check WebSocket server is running
- **Check**: Firewall/security group settings

#### "Low performance on mobile"
- **Solution**: Reduce Unity quality settings
- **Check**: Enable GPU instancing, reduce texture sizes

### Performance Optimization:

1. **Unity Build Settings**:
   ```
   Compression: Gzip
   Code Optimization: Size
   Managed Stripping Level: High
   ```

2. **Asset Optimization**:
   - Compress textures to ASTC/ETC2
   - Use LOD groups for 3D models
   - Optimize audio files (OGG Vorbis)

3. **Runtime Optimization**:
   - Implement object pooling
   - Use Unity's Job System
   - Profile with Unity Profiler

## 📊 Monitoring and Analytics

### Add Analytics:
```javascript
// Add to your HTML template
gtag('event', 'ar_session_start', {
  'event_category': 'WebXR',
  'event_label': 'AR Session Started'
});
```

### Monitor Performance:
```javascript
// Add performance monitoring
setInterval(() => {
  const fps = Math.round(1000 / (performance.now() - lastFrame));
  console.log('FPS:', fps);
}, 1000);
```

## 🎉 Going Live Checklist

- [ ] Unity WebGL build completed successfully
- [ ] Custom HTML template configured
- [ ] WebXR functionality tested on mobile
- [ ] Multiplayer server deployed and accessible
- [ ] HTTPS enabled on both game and server
- [ ] Performance optimized for mobile devices
- [ ] Cross-browser compatibility tested
- [ ] Analytics and monitoring set up
- [ ] Domain name configured (optional)
- [ ] Social sharing implemented

## 🔗 Useful Resources

- **WebXR Device API**: https://immersive-web.github.io/webxr/
- **Unity WebGL Documentation**: https://docs.unity3d.com/Manual/webgl.html
- **WebSocket API**: https://developer.mozilla.org/en-US/docs/Web/API/WebSocket
- **Performance Best Practices**: https://web.dev/performance/

## 🆘 Support

If you encounter issues:
1. Check browser console for errors
2. Test on different devices/browsers
3. Verify server logs for multiplayer issues
4. Use Unity's WebGL debugging tools

Your AR Car Racing game is now ready for the web! 🏎️✨

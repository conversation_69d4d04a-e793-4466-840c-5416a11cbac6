<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AR Car Racing - Web Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            text-align: center;
            padding: 2rem;
            background: rgba(0, 0, 0, 0.3);
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .container {
            flex: 1;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            gap: 2rem;
        }
        
        .demo-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            max-width: 400px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .demo-card h2 {
            font-size: 1.8rem;
            margin-bottom: 1rem;
            color: #00bfff;
        }
        
        .demo-card p {
            margin-bottom: 1.5rem;
            line-height: 1.6;
            opacity: 0.9;
        }
        
        .demo-button {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(45deg, #00bfff, #0080ff);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }
        
        .demo-button:hover {
            background: linear-gradient(45deg, #0080ff, #0060ff);
            transform: scale(1.05);
        }
        
        .demo-button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 10px;
            text-align: left;
        }
        
        .feature h3 {
            color: #00bfff;
            margin-bottom: 0.5rem;
        }
        
        .status {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.3);
        }
        
        .status.success {
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid rgba(0, 255, 0, 0.3);
        }
        
        .status.error {
            background: rgba(255, 0, 0, 0.2);
            border: 1px solid rgba(255, 0, 0, 0.3);
        }
        
        .status.warning {
            background: rgba(255, 255, 0, 0.2);
            border: 1px solid rgba(255, 255, 0, 0.3);
        }
        
        .footer {
            text-align: center;
            padding: 2rem;
            background: rgba(0, 0, 0, 0.3);
            margin-top: auto;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 1rem;
            }
            
            .demo-card {
                max-width: 100%;
            }
        }
        
        .car-animation {
            font-size: 2rem;
            animation: drive 3s ease-in-out infinite;
        }
        
        @keyframes drive {
            0%, 100% { transform: translateX(-10px); }
            50% { transform: translateX(10px); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏎️ AR Car Racing</h1>
        <p>Experience the future of mobile racing with Augmented Reality</p>
        <div class="car-animation">🏎️💨</div>
    </div>
    
    <div class="container">
        <div class="demo-card">
            <h2>🥽 WebXR Demo</h2>
            <p>Test AR functionality in your browser. Works best on mobile devices with Chrome or Edge.</p>
            <button class="demo-button" onclick="testWebXR()">Test AR Support</button>
            <div id="webxr-status" class="status" style="display: none;"></div>
        </div>
        
        <div class="demo-card">
            <h2>🎮 3D Racing Demo</h2>
            <p>Try the 3D racing experience without AR. Works on all devices and browsers.</p>
            <button class="demo-button" onclick="start3DDemo()">Start 3D Demo</button>
            <div id="demo-status" class="status" style="display: none;"></div>
        </div>
        
        <div class="demo-card">
            <h2>👥 Multiplayer Test</h2>
            <p>Test the multiplayer connection and room system.</p>
            <button class="demo-button" onclick="testMultiplayer()">Test Multiplayer</button>
            <div id="multiplayer-status" class="status" style="display: none;"></div>
        </div>
    </div>
    
    <div class="container">
        <div class="demo-card" style="max-width: 800px;">
            <h2>🚀 Game Features</h2>
            <div class="features">
                <div class="feature">
                    <h3>🔍 AR Plane Detection</h3>
                    <p>Automatically detects flat surfaces for track placement</p>
                </div>
                <div class="feature">
                    <h3>🏎️ Realistic Physics</h3>
                    <p>RC-style car physics with acceleration, braking, and steering</p>
                </div>
                <div class="feature">
                    <h3>📱 Touch Controls</h3>
                    <p>Intuitive mobile controls optimized for racing</p>
                </div>
                <div class="feature">
                    <h3>🌐 Multiplayer Racing</h3>
                    <p>Race with friends in shared AR space</p>
                </div>
                <div class="feature">
                    <h3>🏁 Multiple Game Modes</h3>
                    <p>Time Trial, Battle Mode, Checkpoint Race, and more</p>
                </div>
                <div class="feature">
                    <h3>🎨 Customization</h3>
                    <p>Customize your car colors, decals, and unlock new vehicles</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>🛠️ Built with Unity WebGL + WebXR + WebSocket multiplayer</p>
        <p>📱 Best experienced on mobile devices with AR support</p>
        <p>🌐 Requires HTTPS for full AR functionality</p>
    </div>
    
    <script>
        // WebXR Support Test
        async function testWebXR() {
            const statusDiv = document.getElementById('webxr-status');
            statusDiv.style.display = 'block';
            statusDiv.innerHTML = '🔍 Checking WebXR support...';
            statusDiv.className = 'status';
            
            try {
                if (!navigator.xr) {
                    throw new Error('WebXR not available');
                }
                
                const supported = await navigator.xr.isSessionSupported('immersive-ar');
                
                if (supported) {
                    statusDiv.innerHTML = '✅ WebXR AR is supported! You can use full AR features.';
                    statusDiv.className = 'status success';
                } else {
                    statusDiv.innerHTML = '⚠️ WebXR available but AR not supported. You can still play in 3D mode.';
                    statusDiv.className = 'status warning';
                }
            } catch (error) {
                statusDiv.innerHTML = `❌ WebXR not supported: ${error.message}. Try Chrome or Edge on a mobile device.`;
                statusDiv.className = 'status error';
            }
        }
        
        // 3D Demo
        function start3DDemo() {
            const statusDiv = document.getElementById('demo-status');
            statusDiv.style.display = 'block';
            statusDiv.innerHTML = '🎮 3D Demo would start here! (Unity build required)';
            statusDiv.className = 'status warning';
            
            // In a real implementation, this would load the Unity WebGL build
            setTimeout(() => {
                statusDiv.innerHTML = '🎮 To run the full demo, build the Unity project and deploy it to a web server.';
            }, 2000);
        }
        
        // Multiplayer Test
        function testMultiplayer() {
            const statusDiv = document.getElementById('multiplayer-status');
            statusDiv.style.display = 'block';
            statusDiv.innerHTML = '🔗 Testing multiplayer connection...';
            statusDiv.className = 'status';
            
            // Test WebSocket connection
            try {
                const ws = new WebSocket('ws://localhost:8081');
                
                ws.onopen = function() {
                    statusDiv.innerHTML = '✅ Multiplayer server connected! Ready for multiplayer racing.';
                    statusDiv.className = 'status success';
                    ws.close();
                };
                
                ws.onerror = function() {
                    statusDiv.innerHTML = '❌ Multiplayer server not running. Start the server with: npm run dev';
                    statusDiv.className = 'status error';
                };
                
                // Timeout after 5 seconds
                setTimeout(() => {
                    if (ws.readyState === WebSocket.CONNECTING) {
                        ws.close();
                        statusDiv.innerHTML = '⏰ Connection timeout. Make sure the multiplayer server is running.';
                        statusDiv.className = 'status error';
                    }
                }, 5000);
                
            } catch (error) {
                statusDiv.innerHTML = `❌ WebSocket error: ${error.message}`;
                statusDiv.className = 'status error';
            }
        }
        
        // Auto-check WebXR support on page load
        window.addEventListener('load', () => {
            // Add some delay for better UX
            setTimeout(() => {
                if (navigator.xr) {
                    console.log('WebXR detected, checking AR support...');
                } else {
                    console.log('WebXR not available in this browser');
                }
            }, 1000);
        });
        
        // Device info
        console.log('Device Info:', {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            webXR: !!navigator.xr,
            webGL: !!window.WebGLRenderingContext,
            webGL2: !!window.WebGL2RenderingContext
        });
    </script>
</body>
</html>

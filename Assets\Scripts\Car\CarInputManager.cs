using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

namespace ARCarRacing.Car
{
    /// <summary>
    /// Handles touch input for car controls on mobile devices
    /// </summary>
    public class CarInputManager : MonoBehaviour
    {
        [Header("Input Settings")]
        [SerializeField] private bool enableTouchInput = true;
        [SerializeField] private bool enableKeyboardInput = true;
        [SerializeField] private float inputSensitivity = 1f;
        [SerializeField] private float inputSmoothing = 5f;
        
        [Header("Touch Controls")]
        [SerializeField] private RectTransform throttleArea;
        [SerializeField] private RectTransform steerArea;
        [SerializeField] private RectTransform brakeButton;
        
        [Header("Visual Feedback")]
        [SerializeField] private GameObject throttleIndicator;
        [SerializeField] private GameObject steerIndicator;
        [SerializeField] private GameObject brakeIndicator;
        
        // Input values
        public float ThrottleInput { get; private set; }
        public float SteerInput { get; private set; }
        public bool BrakeInput { get; private set; }
        
        // Raw input values (before smoothing)
        private float rawThrottleInput;
        private float rawSteerInput;
        private bool rawBrakeInput;
        
        // Touch tracking
        private int throttleTouchId = -1;
        private int steerTouchId = -1;
        private int brakeTouchId = -1;
        
        // Events
        public System.Action<float, float, bool> OnInputChanged;
        
        private void Update()
        {
            // Handle input based on platform
            if (enableTouchInput && (Application.isMobilePlatform || Input.touchCount > 0))
            {
                HandleTouchInput();
            }
            else if (enableKeyboardInput)
            {
                HandleKeyboardInput();
            }
            
            // Smooth input values
            SmoothInputs();
            
            // Update visual feedback
            UpdateVisualFeedback();
            
            // Notify listeners
            OnInputChanged?.Invoke(ThrottleInput, SteerInput, BrakeInput);
        }
        
        private void HandleTouchInput()
        {
            // Reset inputs if no touches
            if (Input.touchCount == 0)
            {
                ResetInputs();
                return;
            }
            
            // Process each touch
            for (int i = 0; i < Input.touchCount; i++)
            {
                Touch touch = Input.GetTouch(i);
                Vector2 screenPos = touch.position;
                
                // Convert to UI coordinates
                Vector2 localPos;
                RectTransformUtility.ScreenPointToLocalPointInRectangle(
                    transform as RectTransform, screenPos, null, out localPos);
                
                switch (touch.phase)
                {
                    case TouchPhase.Began:
                        HandleTouchBegan(touch.fingerId, screenPos, localPos);
                        break;
                        
                    case TouchPhase.Moved:
                    case TouchPhase.Stationary:
                        HandleTouchMoved(touch.fingerId, screenPos, localPos);
                        break;
                        
                    case TouchPhase.Ended:
                    case TouchPhase.Canceled:
                        HandleTouchEnded(touch.fingerId);
                        break;
                }
            }
        }
        
        private void HandleTouchBegan(int fingerId, Vector2 screenPos, Vector2 localPos)
        {
            // Check which control area was touched
            if (IsPointInRectTransform(throttleArea, screenPos) && throttleTouchId == -1)
            {
                throttleTouchId = fingerId;
                UpdateThrottleInput(screenPos);
            }
            else if (IsPointInRectTransform(steerArea, screenPos) && steerTouchId == -1)
            {
                steerTouchId = fingerId;
                UpdateSteerInput(screenPos);
            }
            else if (IsPointInRectTransform(brakeButton, screenPos) && brakeTouchId == -1)
            {
                brakeTouchId = fingerId;
                rawBrakeInput = true;
            }
        }
        
        private void HandleTouchMoved(int fingerId, Vector2 screenPos, Vector2 localPos)
        {
            // Update inputs for tracked touches
            if (fingerId == throttleTouchId)
            {
                UpdateThrottleInput(screenPos);
            }
            else if (fingerId == steerTouchId)
            {
                UpdateSteerInput(screenPos);
            }
        }
        
        private void HandleTouchEnded(int fingerId)
        {
            // Reset inputs for ended touches
            if (fingerId == throttleTouchId)
            {
                throttleTouchId = -1;
                rawThrottleInput = 0f;
            }
            else if (fingerId == steerTouchId)
            {
                steerTouchId = -1;
                rawSteerInput = 0f;
            }
            else if (fingerId == brakeTouchId)
            {
                brakeTouchId = -1;
                rawBrakeInput = false;
            }
        }
        
        private void UpdateThrottleInput(Vector2 screenPos)
        {
            if (throttleArea == null) return;
            
            // Convert screen position to local position within throttle area
            Vector2 localPos;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                throttleArea, screenPos, null, out localPos);
            
            // Calculate throttle based on vertical position
            float normalizedY = (localPos.y + throttleArea.rect.height * 0.5f) / throttleArea.rect.height;
            normalizedY = Mathf.Clamp01(normalizedY);
            
            // Map to throttle range (-1 to 1, where -1 is reverse)
            rawThrottleInput = (normalizedY - 0.5f) * 2f * inputSensitivity;
            rawThrottleInput = Mathf.Clamp(rawThrottleInput, -1f, 1f);
        }
        
        private void UpdateSteerInput(Vector2 screenPos)
        {
            if (steerArea == null) return;
            
            // Convert screen position to local position within steer area
            Vector2 localPos;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                steerArea, screenPos, null, out localPos);
            
            // Calculate steering based on horizontal position
            float normalizedX = (localPos.x + steerArea.rect.width * 0.5f) / steerArea.rect.width;
            normalizedX = Mathf.Clamp01(normalizedX);
            
            // Map to steering range (-1 to 1)
            rawSteerInput = (normalizedX - 0.5f) * 2f * inputSensitivity;
            rawSteerInput = Mathf.Clamp(rawSteerInput, -1f, 1f);
        }
        
        private void HandleKeyboardInput()
        {
            // Throttle input (W/S or Up/Down arrows)
            float throttle = 0f;
            if (Input.GetKey(KeyCode.W) || Input.GetKey(KeyCode.UpArrow))
                throttle += 1f;
            if (Input.GetKey(KeyCode.S) || Input.GetKey(KeyCode.DownArrow))
                throttle -= 1f;
            
            rawThrottleInput = throttle;
            
            // Steering input (A/D or Left/Right arrows)
            float steer = 0f;
            if (Input.GetKey(KeyCode.A) || Input.GetKey(KeyCode.LeftArrow))
                steer -= 1f;
            if (Input.GetKey(KeyCode.D) || Input.GetKey(KeyCode.RightArrow))
                steer += 1f;
            
            rawSteerInput = steer;
            
            // Brake input (Space)
            rawBrakeInput = Input.GetKey(KeyCode.Space);
        }
        
        private void SmoothInputs()
        {
            // Smooth throttle and steering inputs
            ThrottleInput = Mathf.Lerp(ThrottleInput, rawThrottleInput, Time.deltaTime * inputSmoothing);
            SteerInput = Mathf.Lerp(SteerInput, rawSteerInput, Time.deltaTime * inputSmoothing);
            
            // Brake is immediate (no smoothing)
            BrakeInput = rawBrakeInput;
            
            // Apply dead zones
            if (Mathf.Abs(ThrottleInput) < 0.1f) ThrottleInput = 0f;
            if (Mathf.Abs(SteerInput) < 0.1f) SteerInput = 0f;
        }
        
        private void UpdateVisualFeedback()
        {
            // Update throttle indicator
            if (throttleIndicator != null)
            {
                throttleIndicator.SetActive(Mathf.Abs(ThrottleInput) > 0.1f);
            }
            
            // Update steer indicator
            if (steerIndicator != null)
            {
                steerIndicator.SetActive(Mathf.Abs(SteerInput) > 0.1f);
            }
            
            // Update brake indicator
            if (brakeIndicator != null)
            {
                brakeIndicator.SetActive(BrakeInput);
            }
        }
        
        private bool IsPointInRectTransform(RectTransform rectTransform, Vector2 screenPoint)
        {
            if (rectTransform == null) return false;
            
            return RectTransformUtility.RectangleContainsScreenPoint(
                rectTransform, screenPoint, null);
        }
        
        private void ResetInputs()
        {
            rawThrottleInput = 0f;
            rawSteerInput = 0f;
            rawBrakeInput = false;
            
            throttleTouchId = -1;
            steerTouchId = -1;
            brakeTouchId = -1;
        }
        
        public void SetInputEnabled(bool enabled)
        {
            enableTouchInput = enabled;
            enableKeyboardInput = enabled;
            
            if (!enabled)
            {
                ResetInputs();
                ThrottleInput = 0f;
                SteerInput = 0f;
                BrakeInput = false;
            }
        }
        
        public void SetInputSensitivity(float sensitivity)
        {
            inputSensitivity = Mathf.Clamp(sensitivity, 0.1f, 2f);
        }
        
        private void OnDrawGizmosSelected()
        {
            // Draw input areas for debugging
            if (throttleArea != null)
            {
                Gizmos.color = Color.green;
                Vector3[] corners = new Vector3[4];
                throttleArea.GetWorldCorners(corners);
                
                for (int i = 0; i < 4; i++)
                {
                    Gizmos.DrawLine(corners[i], corners[(i + 1) % 4]);
                }
            }
            
            if (steerArea != null)
            {
                Gizmos.color = Color.blue;
                Vector3[] corners = new Vector3[4];
                steerArea.GetWorldCorners(corners);
                
                for (int i = 0; i < 4; i++)
                {
                    Gizmos.DrawLine(corners[i], corners[(i + 1) % 4]);
                }
            }
        }
    }
}
